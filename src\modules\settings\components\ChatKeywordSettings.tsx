import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Card,
  Button,
  FormItem,
  Input,
  Toggle,
  Modal,
  Icon,
  Alert,
  Typography,
  Select,
  TagsInput,
  Chip
} from '@/shared/components/common';
import { RootState } from '@/shared/store';
import {
  addChatKeyword,
  removeChatKeyword,
  toggleChatKeyword,
  resetChatKeywords
} from '../store/settingsSlice';
import { ChatKeyword } from '../types';
import { userMenuItems } from '@/shared/components/layout/chat-panel/menu-items';
import { useFormErrors } from '@/shared/hooks/form';

// Form schema for adding new keywords
const keywordSchema = z.object({
  path: z.string().min(1, 'Đường dẫn không được để trống').regex(/^\//, 'Đường dẫn phải bắt đầu bằng /'),
  keywords: z.array(z.string()).min(1, 'Phải có ít nhất một từ khóa'),
  description: z.string().max(200, 'Mô tả không được quá 200 ký tự').optional(),
});

type KeywordFormData = z.infer<typeof keywordSchema>;

const ChatKeywordSettings: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { chatKeywords, customKeywords } = useSelector((state: RootState) => state.settings);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const { formRef } = useFormErrors<KeywordFormData>();

  // Tạo options cho Select từ userMenuItems
  const pathOptions = useMemo(() => {
    return userMenuItems.map(item => ({
      value: item.path,
      label: `${item.path} - ${t(item.label, item.label)}`,
    }));
  }, [t]);

  const {
    register,
    handleSubmit,
    reset,
    control,
    setValue
  } = useForm<KeywordFormData>({
    resolver: zodResolver(keywordSchema),
    defaultValues: {
      keywords: [],
      path: '',
      description: ''
    }
  });

  // Combine default and custom keywords, group by path
  const keywordsByPath = useMemo(() => {
    const allKeywords = [...chatKeywords, ...customKeywords];
    const grouped: Record<string, ChatKeyword[]> = {};

    allKeywords.forEach(keyword => {
      if (!grouped[keyword.path]) {
        grouped[keyword.path] = [];
      }
      grouped[keyword.path]!.push(keyword);
    });

    return grouped;
  }, [chatKeywords, customKeywords]);

  const handleAddKeywords = (data: KeywordFormData) => {
    // Tạo một keyword cho mỗi tag được nhập
    data.keywords.forEach((keywordText, index) => {
      const newKeyword: ChatKeyword = {
        id: `custom_${Date.now()}_${index}`,
        keyword: keywordText.trim(),
        path: data.path,
        ...(data.description && { description: data.description }),
        enabled: true,
      };
      dispatch(addChatKeyword(newKeyword));
    });

    reset();
    setIsAddModalOpen(false);
  };

  const handleTagsChange = (tags: string[]) => {
    setValue('keywords', tags);
  };

  const handleToggleKeyword = (id: string) => {
    dispatch(toggleChatKeyword(id));
  };

  const handleRemoveKeyword = (id: string) => {
    dispatch(removeChatKeyword(id));
  };

  const handleResetKeywords = () => {
    dispatch(resetChatKeywords());
  };

  return (
    <Card title={t('settings.chatKeywords.title', 'Cài đặt từ khóa ChatPanel')} className="mb-6">
      <div className="space-y-4">
        {/* Description */}
        <Alert
          type="info"
          title={t('settings.chatKeywords.howItWorks', 'Cách hoạt động')}
          message={t('settings.chatKeywords.description', 'Chọn đường dẫn và nhập nhiều từ khóa bằng cách gõ từ khóa rồi nhấn Enter. Mỗi đường dẫn có thể có nhiều từ khóa để dễ dàng điều hướng.')}
          showIcon={true}
        />

        {/* Add new keyword button */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
          <Typography variant="h6" weight="medium" color="default">
            {t('settings.chatKeywords.keywordList', 'Danh sách từ khóa theo đường dẫn')}
          </Typography>
          <Button
            variant="primary"
            onClick={() => setIsAddModalOpen(true)}
            leftIcon={<Icon name="plus" size="sm" />}
            className="w-full sm:w-auto"
          >
            {t('settings.chatKeywords.addKeyword', 'Thêm từ khóa')}
          </Button>
        </div>

        {/* Keywords list grouped by path */}
        <div className="space-y-4">
          {Object.keys(keywordsByPath).length === 0 ? (
            <div className="text-center py-8">
              <Typography variant="body2" color="muted">
                {t('settings.chatKeywords.noKeywords', 'Chưa có từ khóa nào. Hãy thêm từ khóa đầu tiên!')}
              </Typography>
            </div>
          ) : (
            Object.entries(keywordsByPath).map(([path, keywords]) => (
              <div
                key={path}
                className="border border-border rounded-lg p-4"
              >
                <div className="flex items-center justify-between mb-3">
                  <Typography variant="code" className="px-3 py-1 bg-primary/10 text-primary rounded font-medium">
                    {path}
                  </Typography>
                  <Typography variant="body2" color="muted">
                    {keywords.length} từ khóa
                  </Typography>
                </div>

                <div className="space-y-2">
                  {keywords.map((keyword) => (
                    <div
                      key={keyword.id}
                      className="flex items-center justify-between p-2 bg-muted/50 rounded gap-3"
                    >
                      <div className="flex items-center gap-2 flex-1">
                        <Chip size="sm">
                          {keyword.keyword}
                        </Chip>
                        {keyword.description && (
                          <Typography variant="body2" color="muted" className="text-xs">
                            {keyword.description}
                          </Typography>
                        )}
                      </div>

                      <div className="flex items-center space-x-2 flex-shrink-0">
                        <Toggle
                          checked={keyword.enabled}
                          onChange={() => handleToggleKeyword(keyword.id)}
                          size="sm"
                        />
                        {keyword.id.startsWith('custom_') && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveKeyword(keyword.id)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50 p-1"
                          >
                            <Icon name="trash" size="xs" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Reset button */}
        <div className="flex justify-end pt-4 border-t border-border">
          <Button
            variant="outline"
            onClick={handleResetKeywords}
            className="text-sm"
          >
            {t('settings.chatKeywords.resetToDefault', 'Đặt lại mặc định')}
          </Button>
        </div>
      </div>

      {/* Add keyword modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title={t('settings.chatKeywords.addNewKeyword', 'Thêm từ khóa cho đường dẫn')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsAddModalOpen(false)}
            >
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button
              type="submit"
              variant="primary"
              onClick={handleSubmit(handleAddKeywords)}
            >
              {t('common.add', 'Thêm')}
            </Button>
          </div>
        }
      >
        <form onSubmit={handleSubmit(handleAddKeywords)} className="space-y-4">
          <FormItem
            label={t('settings.chatKeywords.path', 'Đường dẫn')}
            name="path"
            required
          >
            <Controller
              name="path"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value}
                  onChange={field.onChange}
                  options={pathOptions}
                  placeholder={t('settings.chatKeywords.pathPlaceholder', 'Chọn đường dẫn...')}
                  fullWidth
                />
              )}
            />
          </FormItem>

          <FormItem
            label={t('settings.chatKeywords.keywords', 'Từ khóa')}
            name="keywords"
            required
          >
            <div>
              <TagsInput
                fieldName="keywords"
                formRef={formRef}
                placeholder={t('settings.chatKeywords.keywordsPlaceholder', 'Nhập từ khóa và nhấn Enter để thêm')}
                onChange={handleTagsChange}
              />
              <Typography variant="caption" color="muted" className="mt-1">
                {t('settings.chatKeywords.keywordsHelp', 'Ví dụ: trang chủ, home, dashboard')}
              </Typography>
            </div>
          </FormItem>

          <FormItem
            label={t('settings.chatKeywords.description', 'Mô tả (tùy chọn)')}
            name="description"
          >
            <Input
              {...register('description')}
              placeholder={t('settings.chatKeywords.descriptionPlaceholder', 'Mô tả ngắn gọn về nhóm từ khóa này')}
              fullWidth
            />
          </FormItem>
        </form>
      </Modal>
    </Card>
  );
};

export default ChatKeywordSettings;
