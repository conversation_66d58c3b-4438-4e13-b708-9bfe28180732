import React from 'react';
import { Card, Icon } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

interface ProviderSelectionCardsProps {
  onSelectOpenAI: () => void;
  onSelectGoogle: () => void;
}

const ProviderSelectionCards: React.FC<ProviderSelectionCardsProps> = ({
  onSelectOpenAI,
  onSelectGoogle,
}) => {
  const { t } = useTranslation();
  return (
    <div className="grid grid-cols-2 gap-4 mt-4">
      <Card
        onClick={onSelectOpenAI}
        className="cursor-pointer hover:shadow-lg transition-shadow duration-300"
        variant="elevated"
      >
        <div className="p-4 flex items-center space-x-4">
          {/* Icon OpenAI */}
          <div className="flex-shrink-0">
            <Icon name="openai" size="xl" />
          </div>

          {/* Nội dung bên phải */}
          <div className="text-left">
            <h3 className="text-lg font-semibold mb-1">{t('openaiProvider', 'OpenAI Provider')}</h3>
            <p className="text-gray-600 text-sm">
              {t(
                'user-dataset:providerSelection.createDatasetUsingOpenAI',
                'Create dataset using OpenAI'
              )}
            </p>
          </div>
        </div>
      </Card>

      <Card
        onClick={onSelectGoogle}
        className="cursor-pointer hover:shadow-lg transition-shadow duration-300"
        variant="elevated"
      >
        <div className="p-4 flex items-center space-x-4">
          {/* Icon Google */}
          <div className="flex-shrink-0">
            <Icon name="google" size="xl" />
          </div>

          {/* Nội dung bên phải */}
          <div className="text-left">
            <h3 className="text-lg font-semibold mb-1">{t('googleProvider', 'Google Provider')}</h3>
            <p className="text-gray-600 text-sm">
              {t(
                'user-dataset:providerSelection.createDatasetUsingGoogle',
                'Create dataset using Google'
              )}
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ProviderSelectionCards;
