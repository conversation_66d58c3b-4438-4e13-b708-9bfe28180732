/**
 * Types for User Integration Management
 */

import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho loại tích hợp
 */
export enum IntegrationType {
  ANALYTICS = 'ANALYTICS',
  PAYMENT = 'PAYMENT',
  SHIPPING = 'SHIPPING',
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  SOCIAL = 'SOCIAL',
  DATABASE = 'DATABASE',
  API = 'API',
  WEBHOOK = 'WEBHOOK',
  OTHER = 'OTHER',
}

/**
 * Enum cho trạng thái tích hợp
 */
export enum IntegrationStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  ERROR = 'ERROR',
}

/**
 * Interface cho thông tin tích hợp của user
 */
export interface UserIntegrationDto {
  /**
   * ID của tích hợp
   */
  id: number;

  /**
   * Tên tích hợp
   */
  integrationName: string;

  /**
   * Loại tích hợp
   */
  type: IntegrationType;

  /**
   * Trạng thái tích hợp
   */
  status?: IntegrationStatus;

  /**
   * ID của người dùng sở hữu tích hợp
   */
  userId: number;

  /**
   * Thông tin cấu hình tích hợp
   */
  info: Record<string, unknown>;

  /**
   * Loại sở hữu tích hợp (USER, ADMIN, SYSTEM)
   */
  ownedType: 'USER' | 'ADMIN' | 'SYSTEM';

  /**
   * Thời gian tạo (ISO string)
   */
  createdAt: string;

  /**
   * Thời gian cập nhật (ISO string)
   */
  updatedAt?: string;

  /**
   * Thông tin người tạo
   */
  createdBy?: {
    id: number;
    name: string;
    avatar?: string;
  };

  /**
   * Thông tin người cập nhật
   */
  updatedBy?: {
    id: number;
    name: string;
    avatar?: string;
  };
}

/**
 * Interface cho query parameters
 */
export interface UserIntegrationQueryDto extends QueryDto {
  /**
   * Lọc theo loại tích hợp
   */
  type?: IntegrationType;

  /**
   * Lọc theo trạng thái
   */
  status?: IntegrationStatus;

  /**
   * Tìm kiếm theo tên
   */
  search?: string;
}

/**
 * Interface cho tạo tích hợp mới
 */
export interface CreateUserIntegrationDto {
  /**
   * Tên tích hợp
   */
  integrationName: string;

  /**
   * Loại tích hợp
   */
  type: IntegrationType;

  /**
   * Thông tin cấu hình tích hợp
   */
  info: Record<string, unknown>;

  /**
   * Loại sở hữu tích hợp (USER, ADMIN, SYSTEM)
   */
  ownedType?: 'USER' | 'ADMIN' | 'SYSTEM';
}

/**
 * Interface cho cập nhật tích hợp
 */
export interface UpdateUserIntegrationDto {
  /**
   * Tên tích hợp
   */
  integrationName?: string;

  /**
   * Loại tích hợp
   */
  type?: IntegrationType;

  /**
   * Thông tin cấu hình tích hợp
   */
  info?: Record<string, unknown>;

  /**
   * Loại sở hữu tích hợp (USER, ADMIN, SYSTEM)
   */
  ownedType?: 'USER' | 'ADMIN' | 'SYSTEM';

  /**
   * Trạng thái tích hợp
   */
  status?: IntegrationStatus;
}

/**
 * Interface cho thông tin cấu hình Twilio SMS
 */
export interface TwilioSmsInfo {
  /**
   * Twilio Auth Token
   */
  TWILIO_AUTH_TOKEN: string;

  /**
   * Twilio Base Domain
   */
  TWILIO_BASE_DOMAIN: string;
}

/**
 * Interface cho tạo tích hợp Twilio SMS mới
 */
export interface CreateTwilioSmsIntegrationDto {
  /**
   * Tên tích hợp
   */
  integrationName: string;

  /**
   * Thông tin cấu hình Twilio SMS
   */
  info: TwilioSmsInfo;
}

/**
 * Utility function để lấy nhãn hiển thị cho loại tích hợp
 */
export const getIntegrationTypeLabel = (type: IntegrationType): string => {
  const typeLabels: Record<IntegrationType, string> = {
    [IntegrationType.ANALYTICS]: 'Phân tích',
    [IntegrationType.PAYMENT]: 'Thanh toán',
    [IntegrationType.SHIPPING]: 'Vận chuyển',
    [IntegrationType.EMAIL]: 'Email',
    [IntegrationType.SMS]: 'SMS',
    [IntegrationType.SOCIAL]: 'Mạng xã hội',
    [IntegrationType.DATABASE]: 'Cơ sở dữ liệu',
    [IntegrationType.API]: 'API',
    [IntegrationType.WEBHOOK]: 'Webhook',
    [IntegrationType.OTHER]: 'Khác',
  };

  return typeLabels[type] || type;
};

/**
 * Utility function để lấy nhãn hiển thị cho trạng thái tích hợp
 */
export const getIntegrationStatusLabel = (status: IntegrationStatus): string => {
  const statusLabels: Record<IntegrationStatus, string> = {
    [IntegrationStatus.ACTIVE]: 'Hoạt động',
    [IntegrationStatus.INACTIVE]: 'Không hoạt động',
    [IntegrationStatus.PENDING]: 'Đang chờ',
    [IntegrationStatus.ERROR]: 'Lỗi',
  };

  return statusLabels[status] || status;
};

/**
 * Utility function để lấy màu cho trạng thái tích hợp
 */
export const getIntegrationStatusColor = (status: IntegrationStatus): string => {
  const statusColors: Record<IntegrationStatus, string> = {
    [IntegrationStatus.ACTIVE]: 'success',
    [IntegrationStatus.INACTIVE]: 'default',
    [IntegrationStatus.PENDING]: 'warning',
    [IntegrationStatus.ERROR]: 'error',
  };

  return statusColors[status] || 'default';
};

/**
 * Utility function để lấy icon cho loại tích hợp
 */
export const getIntegrationTypeIcon = (type: IntegrationType): string => {
  const typeIcons: Record<IntegrationType, string> = {
    [IntegrationType.ANALYTICS]: 'chart',
    [IntegrationType.PAYMENT]: 'credit-card',
    [IntegrationType.SHIPPING]: 'truck',
    [IntegrationType.EMAIL]: 'mail',
    [IntegrationType.SMS]: 'message-circle',
    [IntegrationType.SOCIAL]: 'users',
    [IntegrationType.DATABASE]: 'database',
    [IntegrationType.API]: 'code',
    [IntegrationType.WEBHOOK]: 'webhook',
    [IntegrationType.OTHER]: 'grid',
  };

  return typeIcons[type] || 'grid';
};

/**
 * Utility function để lấy danh sách options cho select loại tích hợp
 */
export const getIntegrationTypeOptions = () => {
  return Object.values(IntegrationType).map(type => ({
    value: type,
    label: getIntegrationTypeLabel(type),
    icon: getIntegrationTypeIcon(type),
  }));
};

/**
 * Utility function để lấy danh sách options cho select trạng thái tích hợp
 */
export const getIntegrationStatusOptions = () => {
  return Object.values(IntegrationStatus).map(status => ({
    value: status,
    label: getIntegrationStatusLabel(status),
    color: getIntegrationStatusColor(status),
  }));
};
