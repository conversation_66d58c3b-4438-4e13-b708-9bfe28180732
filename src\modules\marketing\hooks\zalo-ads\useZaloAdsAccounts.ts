import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/shared/api';
import type {
  ZaloAdsAccountDto,
  ZaloAdsAccountQueryDto,
  CreateZaloAdsAccountDto,
} from '../../types/zalo-ads.types';

// Temporary type until it's added to zalo-ads.types
interface UpdateZaloAdsAccountDto {
  accountName?: string;
  businessName?: string;
  accessToken?: string;
  refreshToken?: string;
}
import type { PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Hook để lấy danh sách tài khoản Zalo Ads
 */
export function useZaloAdsAccounts(query: ZaloAdsAccountQueryDto) {
  return useQuery({
    queryKey: ['zalo-ads-accounts', query],
    queryFn: async (): Promise<PaginatedResult<ZaloAdsAccountDto>> => {
      const params = new URLSearchParams();

      if (query.page) params.append('page', query.page.toString());
      if (query.limit) params.append('limit', query.limit.toString());
      if (query.search) params.append('search', query.search);
      if (query.status) params.append('status', query.status);
      if (query.sortBy) params.append('sortBy', query.sortBy);
      if (query.sortDirection) params.append('sortDirection', query.sortDirection);

      const response = await apiClient.get<PaginatedResult<ZaloAdsAccountDto>>(
        `/marketing/zalo?${params.toString()}`
      );

      return response.result;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

/**
 * Hook để lấy chi tiết tài khoản Zalo Ads
 */
export function useZaloAdsAccount(accountId: string) {
  return useQuery({
    queryKey: ['zalo-ads-account', accountId],
    queryFn: async (): Promise<ZaloAdsAccountDto> => {
      const response = await apiClient.get<ZaloAdsAccountDto>(
        `/marketing/zalo-ads/accounts/${accountId}`
      );

      return response.result;
    },
    enabled: !!accountId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

/**
 * Hook để kết nối tài khoản Zalo Ads mới
 */
export function useConnectZaloAdsAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateZaloAdsAccountDto): Promise<ZaloAdsAccountDto> => {
      const response = await apiClient.post<ZaloAdsAccountDto>(
        '/marketing/zalo-ads/accounts',
        data
      );

      return response.result;
    },
    onSuccess: () => {
      // Invalidate và refetch accounts list
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-accounts'] });
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-metrics'] });
    },
  });
}

/**
 * Hook để cập nhật tài khoản Zalo Ads
 */
export function useUpdateZaloAdsAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      accountId,
      data,
    }: {
      accountId: string;
      data: UpdateZaloAdsAccountDto;
    }): Promise<ZaloAdsAccountDto> => {
      const response = await apiClient.put<ZaloAdsAccountDto>(
        `/marketing/zalo-ads/accounts/${accountId}`,
        data
      );

      return response.result;
    },
    onSuccess: data => {
      // Update cache
      queryClient.setQueryData(['zalo-ads-account', data.accountId], data);
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-accounts'] });
    },
  });
}

/**
 * Hook để xóa tài khoản Zalo Ads
 */
export function useDeleteZaloAdsAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (accountId: string): Promise<void> => {
      await apiClient.delete(`/marketing/zalo-ads/accounts/${accountId}`);
    },
    onSuccess: () => {
      // Invalidate accounts list
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-accounts'] });
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-metrics'] });
    },
  });
}

/**
 * Hook để refresh access token
 */
export function useRefreshZaloAdsToken() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (accountId: string): Promise<ZaloAdsAccountDto> => {
      const response = await apiClient.post<ZaloAdsAccountDto>(
        `/marketing/zalo-ads/accounts/${accountId}/refresh-token`
      );

      return response.result;
    },
    onSuccess: data => {
      // Update cache
      queryClient.setQueryData(['zalo-ads-account', data.accountId], data);
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-accounts'] });
    },
  });
}

/**
 * Hook để lấy thông tin số dư tài khoản
 */
export function useZaloAdsAccountBalance(accountId: string) {
  return useQuery({
    queryKey: ['zalo-ads-account-balance', accountId],
    queryFn: async (): Promise<{ balance: number; currency: string }> => {
      const response = await apiClient.get<{ balance: number; currency: string }>(
        `/marketing/zalo-ads/accounts/${accountId}/balance`
      );

      return response.result;
    },
    enabled: !!accountId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
}

/**
 * Hook để sync tài khoản với Zalo Ads API
 */
export function useSyncZaloAdsAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (accountId: string): Promise<ZaloAdsAccountDto> => {
      const response = await apiClient.post<ZaloAdsAccountDto>(
        `/marketing/zalo-ads/accounts/${accountId}/sync`
      );

      return response.result;
    },
    onSuccess: data => {
      // Update cache
      queryClient.setQueryData(['zalo-ads-account', data.accountId], data);
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-accounts'] });
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-account-balance', data.accountId] });
    },
  });
}
