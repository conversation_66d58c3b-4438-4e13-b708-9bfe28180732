import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Typography,
  Button,
  Input,
  Select,
  Textarea,
  Card,
  Icon,
} from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import { formatDate } from '@/shared/utils/date';

// Import types
import {
  UserIntegrationDto,
  CreateUserIntegrationDto,
  UpdateUserIntegrationDto,
  IntegrationType,
  getIntegrationTypeOptions,
  getIntegrationTypeLabel,
} from '../types/user-integration.types';
import {
  createUserIntegration,
} from '../services/user-integration.api';

interface UserIntegrationFormProps {
  /**
   * Dữ liệu ban đầu cho form (khi edit)
   */
  initialData?: UserIntegrationDto;

  /**
   * Callback khi submit form thành công
   */
  onSubmit: (data: UserIntegrationDto) => void;

  /**
   * Callback khi cancel form
   */
  onCancel: () => void;

  /**
   * Trạng thái loading
   */
  loading?: boolean;

  /**
   * Chế độ form: create, edit, view
   */
  mode?: 'create' | 'edit' | 'view';
}

// Form data interface
interface FormData {
  integrationName: string;
  type: IntegrationType;
  info: Record<string, unknown>;
  createdAt?: string;
}

// Validation schema
const integrationSchema = z.object({
  integrationName: z
    .string()
    .min(1, 'Tên tích hợp không được để trống')
    .max(255, 'Tên tích hợp không được vượt quá 255 ký tự'),
  type: z.nativeEnum(IntegrationType, {
    errorMap: () => ({ message: 'Vui lòng chọn loại tích hợp' }),
  }),
  info: z.record(z.unknown()).default({}),
  createdAt: z.string().optional(),
}) as z.ZodType<FormData>;

/**
 * Form component cho tạo/chỉnh sửa/xem tích hợp
 */
const UserIntegrationForm: React.FC<UserIntegrationFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  mode = 'create',
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(integrationSchema),
    defaultValues: {
      integrationName: initialData?.integrationName || '',
      type: initialData?.type || IntegrationType.API,
      info: initialData?.info || {},
      ...(mode === 'view' && initialData?.createdAt && {
        createdAt: initialData.createdAt,
      }),
    },
  });

  // Reset form when initialData changes
  useEffect(() => {
    if (initialData) {
      reset({
        integrationName: initialData.integrationName,
        type: initialData.type,
        info: initialData.info || {},
        ...(mode === 'view' && initialData.createdAt && {
          createdAt: initialData.createdAt,
        }),
      });
    }
  }, [initialData, reset, mode]);

  // Watch form values
  const watchedType = watch('type');

  // Handle form submission
  const onFormSubmit: SubmitHandler<FormData> = async (data) => {
    try {
      setIsSubmitting(true);

      if (mode === 'create') {
        const createData: CreateUserIntegrationDto = {
          integrationName: data.integrationName,
          type: data.type,
          info: data.info || {},
        };
        const response = await createUserIntegration(createData);
        NotificationUtil.success({
          title: t('common:success', 'Thành công'),
          message: t('integration:createSuccess', 'Tạo tích hợp thành công'),
        });
        onSubmit(response.result);
      } else {
        // For edit mode, just pass the data to parent component
        const updateData: UpdateUserIntegrationDto = {
          integrationName: data.integrationName,
          type: data.type,
          info: data.info || {},
        };
        onSubmit(updateData as UserIntegrationDto); // Parent will handle the API call
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      const errorMessage = mode === 'create'
        ? t('integration:createError', 'Lỗi khi tạo tích hợp')
        : t('integration:updateError', 'Lỗi khi cập nhật tích hợp');
      NotificationUtil.error({
        title: t('common:error', 'Lỗi'),
        message: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get form title
  const getFormTitle = () => {
    switch (mode) {
      case 'create':
        return t('integration:createIntegration', 'Tạo tích hợp mới');
      case 'edit':
        return t('integration:editIntegration', 'Chỉnh sửa tích hợp');
      case 'view':
        return t('integration:viewIntegration', 'Xem chi tiết tích hợp');
      default:
        return '';
    }
  };

  // Get form description
  const getFormDescription = () => {
    switch (mode) {
      case 'create':
        return t('integration:createIntegrationDescription', 'Tạo tích hợp mới để kết nối với dịch vụ bên ngoài');
      case 'edit':
        return t('integration:editIntegrationDescription', 'Chỉnh sửa thông tin tích hợp');
      case 'view':
        return t('integration:viewIntegrationDescription', 'Xem thông tin chi tiết của tích hợp');
      default:
        return '';
    }
  };

  const isReadOnly = mode === 'view';
  const typeOptions = getIntegrationTypeOptions();

  return (
    <div className="w-full max-w-2xl mx-auto">
      <Card className="p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
              <Icon name="integration" className="w-5 h-5 text-primary" />
            </div>
            <div>
              <Typography variant="h3" className="text-foreground">
                {getFormTitle()}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {getFormDescription()}
              </Typography>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          {/* Integration Name */}
          <div>
            <Input
              label={t('integration:name', 'Tên tích hợp')}
              placeholder={t('integration:namePlaceholder', 'Nhập tên tích hợp')}
              error={errors.integrationName?.message}
              disabled={isReadOnly}
              className={isReadOnly ? "w-full" : ""}
              {...register('integrationName')}
            />
          </div>

          {/* Integration Type */}
          <div>
            {isReadOnly ? (
              <Input
                label={t('integration:type', 'Loại tích hợp')}
                value={getIntegrationTypeLabel(watchedType)}
                disabled={true}
              />
            ) : (
              <Select
                label={t('integration:type', 'Loại tích hợp')}
                placeholder={t('integration:selectType', 'Chọn loại tích hợp')}
                options={typeOptions}
                error={errors.type?.message}
                disabled={isReadOnly}
                value={watchedType}
                onChange={(value) => setValue('type', value as IntegrationType)}
              />
            )}
          </div>



          {/* Info Configuration */}
          <div>
            {isReadOnly ? (
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  {t('integration:info', 'Thông tin cấu hình')}
                </label>
                <div className="bg-muted/50 border border-border rounded-md p-3">
                  <pre className="text-sm text-foreground whitespace-pre-wrap break-all font-mono">
                    {JSON.stringify(watch('info') || {}, null, 2)}
                  </pre>
                </div>
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  {t('integration:info', 'Thông tin cấu hình')}
                </label>
                <Textarea
                  placeholder={t('integration:infoPlaceholder', 'Nhập thông tin cấu hình dưới dạng JSON')}
                  rows={6}
                  disabled={isReadOnly}
                  fullWidth
                  className="font-mono text-sm bg-muted/50"
                  value={JSON.stringify(watch('info') || {}, null, 2)}
                  onChange={(e) => {
                    try {
                      const parsed = JSON.parse(e.target.value);
                      setValue('info', parsed);
                    } catch {
                      // Invalid JSON, keep the string value for user to fix
                    }
                  }}
                />
                {errors.info?.['message'] && (
                  <p className="mt-1 text-sm text-error">{String(errors.info['message'])}</p>
                )}
              </div>
            )}
          </div>

          {/* Display additional info in view mode */}
          {mode === 'view' && initialData && (
            <>
              {/* Created At */}
              <div>
                <Input
                  label={t('integration:createdAt', 'Ngày tạo')}
                  value={initialData.createdAt ? formatDate(new Date(initialData.createdAt)) : ''}
                  disabled={true}
                />
              </div>
            </>
          )}

          {/* Form Actions */}
          {!isReadOnly && (
            <div className="flex items-center justify-end space-x-3 pt-4 border-t border-border">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting || loading}
              >
                {t('common:cancel', 'Hủy')}
              </Button>
              <Button
                type="submit"
                variant="primary"
                isLoading={isSubmitting || loading}
                disabled={isSubmitting || loading}
              >
                {mode === 'create' 
                  ? t('common:create', 'Tạo')
                  : t('common:save', 'Lưu')
                }
              </Button>
            </div>
          )}

          {/* View mode actions */}
          {isReadOnly && (
            <div className="flex items-center justify-end pt-4 border-t border-border">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
              >
                {t('common:close', 'Đóng')}
              </Button>
            </div>
          )}
        </form>
      </Card>
    </div>
  );
};

export default UserIntegrationForm;
