import React, { useState, useEffect, useRef, useCallback, forwardRef, useImperativeHandle } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { debounce } from 'lodash';
import { Loader2 } from 'lucide-react';
import { Icon, SelectOption } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';
import { TagService } from '../../services/tag.service'; // Admin tag service
import { Tag, TagStatus } from '../../types/tag.types'; // Admin tag types

export interface AdminTagsSelectWithPaginationProps {
  /**
   * Giá trị đã chọn (array of tag IDs)
   */
  value?: number[];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: number[]) => void;

  /**
   * Thời gian debounce cho search (ms)
   */
  debounceTime?: number;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Name attribute
   */
  name?: string;

  /**
   * ID attribute
   */
  id?: string;

  /**
   * CSS class
   */
  className?: string;

  /**
   * Error message
   */
  error?: string;

  /**
   * Helper text
   */
  helperText?: string;

  /**
   * Size
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Full width
   */
  fullWidth?: boolean;

  /**
   * Số items mỗi trang
   */
  itemsPerPage?: number;

  /**
   * Tự động load trang đầu tiên khi mở
   */
  autoLoadInitial?: boolean;

  /**
   * Message khi không có options
   */
  noOptionsMessage?: string;

  /**
   * Message khi đang loading
   */
  loadingMessage?: string;

  /**
   * Search chỉ khi nhấn Enter
   */
  searchOnEnter?: boolean;
}

/**
 * Component AdminTagsSelectWithPagination - Select tags với khả năng tải dữ liệu từ Admin API có phân trang
 */
const AdminTagsSelectWithPagination = forwardRef<HTMLInputElement, AdminTagsSelectWithPaginationProps>(
  (
    {
      value = [],
      onChange,
      debounceTime = 300,
      placeholder = '',
      label,
      disabled = false,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      itemsPerPage = 20,
      autoLoadInitial = true,
      noOptionsMessage,
      loadingMessage,
      searchOnEnter = true,
    },
    ref
  ) => {
    useTheme(); // Keep the hook call to avoid React hooks rules violation
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    const [options, setOptions] = useState<Tag[]>([]);
    const [loading, setLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [pendingSearchTerm, setPendingSearchTerm] = useState('');
    const lastSearchTermRef = useRef('');
    const hasLoadedInitialRef = useRef(false);
    const [hasSearched, setHasSearched] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMore, setHasMore] = useState(false);
    const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
    const [dropdownPosition, setDropdownPosition] = useState<{
      top: number;
      left: number;
      width: number;
    } | null>(null);

    const selectRef = useRef<HTMLDivElement>(null);
    const hiddenInputRef = useRef<HTMLInputElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10 text-sm',
      lg: 'h-12 text-base',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Update selectedTags when value prop changes
    useEffect(() => {
      if (value && value.length > 0) {
        // Find selected tags from current options or keep existing selectedTags
        const newSelectedTags = value.map(tagId => {
          const existingTag = selectedTags.find(tag => tag.id === tagId);
          if (existingTag) return existingTag;

          const optionTag = options.find(tag => tag.id === tagId);
          if (optionTag) return optionTag;

          // If tag not found, create a placeholder
          return { id: tagId, name: `Tag ${tagId}`, status: TagStatus.ACTIVE, createdAt: '', updatedAt: '' };
        });
        setSelectedTags(newSelectedTags);
      } else {
        setSelectedTags([]);
      }
    }, [value, options, selectedTags]);

    // Load data function using Admin TagService
    const loadDataRef = useRef<(params: {
      search?: string;
      page?: number;
      reset?: boolean;
    }) => Promise<void>>();

    loadDataRef.current = async (params: {
      search?: string;
      page?: number;
      reset?: boolean;
    }) => {
      const { search = '', page = 1, reset = false } = params;

      // Tránh gọi API với cùng search term và page = 1 (trừ khi reset = true)
      if (page === 1 && !reset && search === lastSearchTermRef.current) {
        return;
      }

      // Nếu đang loading và là cùng một search term, không gọi API
      if (loading && page === 1 && search === searchTerm) {
        return;
      }

      // Cập nhật last search term khi search mới (page = 1)
      if (page === 1) {
        lastSearchTermRef.current = search;
        setHasSearched(true);
      }

      setLoading(true);
      try {
        // Use Admin TagService instead of user TagService
        const result = await TagService.getTags({
          search,
          page,
          limit: itemsPerPage,
        });

        const tags = result.result?.items || [];

        if (reset || page === 1) {
          setOptions(tags);
        } else {
          setOptions(prev => [...prev, ...tags]);
        }

        const meta = result.result?.meta;
        if (meta) {
          setCurrentPage(meta.currentPage);
          setHasMore(meta.currentPage < meta.totalPages);
        }
      } catch (error) {
        console.error('Error loading admin tags:', error);
        if (reset || page === 1) {
          setOptions([]);
        }
      } finally {
        setLoading(false);
      }
    };

    // Create debounced search function
    const debouncedSearchRef = useRef<ReturnType<typeof debounce>>();

    useEffect(() => {
      debouncedSearchRef.current?.cancel();

      debouncedSearchRef.current = debounce((search: string) => {
        if (!searchOnEnter) {
          if (search !== searchTerm) {
            setSearchTerm(search);
            setCurrentPage(1);
            loadDataRef.current?.({ search, page: 1, reset: true });
          }
        }
      }, debounceTime);

      return () => {
        debouncedSearchRef.current?.cancel();
      };
    }, [debounceTime, searchOnEnter, searchTerm]);

    // Handle search input change
    const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setPendingSearchTerm(value);

      if (!searchOnEnter && debouncedSearchRef.current) {
        debouncedSearchRef.current(value);
      }
    };

    // Handle search on Enter
    const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter' && searchOnEnter) {
        e.preventDefault();

        if (pendingSearchTerm !== searchTerm) {
          setSearchTerm(pendingSearchTerm);
          setCurrentPage(1);
          loadDataRef.current?.({ search: pendingSearchTerm, page: 1, reset: true });
        }
      }
    };

    // Load more data when scrolling
    const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

      if (
        scrollHeight - scrollTop <= clientHeight + 50 && // 50px threshold
        hasMore &&
        !loading
      ) {
        loadDataRef.current?.({ search: searchTerm, page: currentPage + 1 });
      }
    }, [hasMore, loading, searchTerm, currentPage]);

    // Load initial data when dropdown opens
    useEffect(() => {
      if (isOpen && autoLoadInitial && !hasLoadedInitialRef.current) {
        hasLoadedInitialRef.current = true;
        loadDataRef.current?.({ search: '', page: 1, reset: true });
      }
    }, [isOpen, autoLoadInitial]);

    // Handle option click
    const handleOptionClick = (tag: Tag) => {
      const isSelected = selectedTags.some(selectedTag => selectedTag.id === tag.id);
      let newSelectedTags: Tag[];

      if (isSelected) {
        // Remove tag
        newSelectedTags = selectedTags.filter(selectedTag => selectedTag.id !== tag.id);
      } else {
        // Add tag
        newSelectedTags = [...selectedTags, tag];
      }

      setSelectedTags(newSelectedTags);

      // Call onChange with the new tag IDs
      if (onChange) {
        onChange(newSelectedTags.map(tag => tag.id));
      }

      // Clear search input when option is selected và reset search
      setPendingSearchTerm('');

      // Reset search term về empty để load lại data ban đầu
      if (searchTerm !== '') {
        setSearchTerm('');
        setCurrentPage(1);
        lastSearchTermRef.current = '';
        loadDataRef.current?.({ search: '', page: 1, reset: true });
      }
    };

    // Remove selected tag
    const handleRemoveTag = (tagId: number) => {
      const newSelectedTags = selectedTags.filter(tag => tag.id !== tagId);
      setSelectedTags(newSelectedTags);

      if (onChange) {
        onChange(newSelectedTags.map(tag => tag.id));
      }
    };

    // Get display value
    const getDisplayValue = () => {
      if (selectedTags.length === 0) return placeholder;
      return t('common.selected', { count: selectedTags.length });
    };

    // Calculate dropdown position
    const calculateDropdownPosition = useCallback(() => {
      if (!selectRef.current) return;

      const rect = selectRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const dropdownHeight = 300; // max-height of dropdown

      let top = rect.bottom + window.scrollY;

      // If dropdown would go below viewport, show it above the select
      if (rect.bottom + dropdownHeight > viewportHeight) {
        top = rect.top + window.scrollY - dropdownHeight;
      }

      setDropdownPosition({
        top,
        left: rect.left + window.scrollX,
        width: rect.width,
      });
    }, []);

    // Update dropdown position when opening
    useEffect(() => {
      if (isOpen) {
        calculateDropdownPosition();
        // Focus search input when dropdown opens
        setTimeout(() => {
          searchInputRef.current?.focus();
        }, 0);
      } else {
        setDropdownPosition(null);
      }
    }, [isOpen, calculateDropdownPosition]);

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
          setIsOpen(false);
        }
      };

      if (isOpen) {
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
      }

      // Return empty cleanup function when not adding listener
      return () => {};
    }, [isOpen]);

    // Expose ref methods
    useImperativeHandle(ref, () => hiddenInputRef.current as HTMLInputElement, []);

    // Render single option
    const renderSingleOption = (tag: Tag) => {
      const isSelected = selectedTags.some(selectedTag => selectedTag.id === tag.id);

      return (
        <SelectOption
          key={`option-${tag.id}`}
          value={tag.id}
          label={tag.name}
          disabled={false}
          selected={isSelected}
          onClick={() => handleOptionClick(tag)}
          data={tag as unknown as Record<string, unknown>}
        />
      );
    };

    return (
      <div className={`relative ${widthClass} ${className}`} ref={selectRef}>
        {/* Hidden input for form submission */}
        <input
          type="hidden"
          name={name}
          id={id}
          value={selectedTags.map(tag => tag.id).join(',')}
          ref={hiddenInputRef}
        />

        {/* Label */}
        {label && <label className="block text-sm font-medium mb-1">{label}</label>}

        {/* Select trigger */}
        <div
          className={`
          flex items-center justify-between px-3
          border-0 rounded-md bg-card-muted text-foreground
          ${sizeClasses}
          ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
          ${error ? 'ring-1 ring-error' : ''}
          ${isOpen ? 'ring-2 ring-primary/30' : ''}
          ${fullWidth ? 'w-full' : ''}
        `}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <div className="flex-grow truncate">{getDisplayValue()}</div>
          <Icon
            name={isOpen ? 'chevron-up' : 'chevron-down'}
            size="sm"
            className="ml-2 flex-shrink-0 text-muted-foreground"
          />
        </div>

        {/* Selected tags display */}
        {selectedTags.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-1">
            {selectedTags.map((tag) => (
              <div
                key={tag.id}
                className="inline-flex items-center gap-1 px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-sm"
              >
                <span>{tag.name}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveTag(tag.id)}
                  className="ml-1 text-secondary-foreground/60 hover:text-secondary-foreground transition-colors"
                  aria-label={`Remove ${tag.name}`}
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Error message */}
        {error && (
          <p className="mt-1 text-sm text-destructive">{error}</p>
        )}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-muted-foreground">{helperText}</p>
        )}

        {/* Dropdown Portal */}
        {isOpen && dropdownPosition && createPortal(
          <div
            className="tags-select-dropdown fixed z-[99999] bg-card rounded-md shadow-lg border-0 animate-fade-in"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              width: `${dropdownPosition.width}px`,
              maxHeight: '300px',
            }}
          >
            {/* Search input */}
            <div className="sticky top-0 p-2 bg-card border-b-0">
              <div className="relative">
                <input
                  ref={searchInputRef}
                  type="text"
                  value={pendingSearchTerm}
                  onChange={handleSearchInputChange}
                  onKeyDown={handleSearchKeyDown}
                  placeholder={
                    searchOnEnter
                      ? t('common.searchPressEnter', 'Tìm kiếm... (Nhấn Enter)')
                      : t('common.search', 'Tìm kiếm...')
                  }
                  className="w-full px-3 py-1 pr-8 text-sm border-0 rounded-md focus:outline-none focus:ring-1 focus:ring-primary/20 bg-card-muted text-foreground"
                  onClick={e => e.stopPropagation()}
                />
                {/* Clear search button */}
                {(pendingSearchTerm || searchTerm) && (
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setPendingSearchTerm('');
                      setSearchTerm('');
                      setCurrentPage(1);
                      lastSearchTermRef.current = '';
                      loadDataRef.current?.({ search: '', page: 1, reset: true });
                    }}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    <Icon name="x" size="sm" />
                  </button>
                )}
              </div>
            </div>

            {/* Options */}
            <div
              className="max-h-60 overflow-auto custom-scrollbar auto-hide"
              role="listbox"
              aria-multiselectable={true}
              onScroll={handleScroll}
            >
              {loading && options.length === 0 ? (
                <div className="px-4 py-2 text-sm text-muted flex items-center">
                  <Icon name="loading" className="animate-spin mr-2" size="sm" />
                  {loadingMessage || t('common.loading', 'Loading...')}
                </div>
              ) : options.length > 0 ? (
                <>
                  {options.map(tag => renderSingleOption(tag))}

                  {/* Loading more indicator */}
                  {loading && options.length > 0 && (
                    <div className="flex items-center justify-center p-2 border-t">
                      <Loader2 size={14} className="animate-spin mr-2" />
                      <span className="text-xs text-muted-foreground">
                        {t('common.loadingMore', 'Đang tải thêm...')}
                      </span>
                    </div>
                  )}
                </>
              ) : hasSearched ? (
                <div className="px-4 py-2 text-sm text-muted">
                  {noOptionsMessage || t('common.noOptions', 'Không có tùy chọn nào')}
                </div>
              ) : null}
            </div>
          </div>,
          document.body
        )}
      </div>
    );
  }
);

AdminTagsSelectWithPagination.displayName = 'AdminTagsSelectWithPagination';

export default AdminTagsSelectWithPagination;
