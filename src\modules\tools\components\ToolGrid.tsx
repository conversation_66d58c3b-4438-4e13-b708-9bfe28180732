import React from 'react';
import { ToolListItem } from '../types/tool.types';
import ToolCard from './ToolCard';
import { ResponsiveGrid } from '@/shared/components/common';

interface ToolGridProps {
  tools: ToolListItem[];
  onViewTool?: (tool: ToolListItem) => void;
  onDeleteTool?: (tool: ToolListItem) => void;
  onToggleActive?: (toolId: string) => void;
  onViewVersions?: (toolId: string) => void;
}

/**
 * Component hiển thị danh sách Tools dạng grid
 * Sử dụng ResponsiveGrid để tự động điều chỉnh số cột dựa trên kích thước màn hình
 *
 * Responsive:
 * - Mobile (<640px): 1 column
 * - Small Tablet (640px-767px): 1-2 columns
 * - Tablet (768px-1023px): 2 columns
 * - Desktop (1024px-1279px): 2-3 columns
 * - Large Desktop (≥1280px): 3-4 columns
 */
const ToolGrid: React.FC<ToolGridProps> = ({ tools, onViewTool, onDeleteTool, onToggleActive }) => {
  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
      gap={{ xs: 4, md: 5, lg: 6 }}
    >
      {tools.map(tool => (
        <div key={tool.id} className="h-full">
          <ToolCard
            tool={tool}
            {...(onViewTool && { onView: onViewTool })}
            {...(onDeleteTool && { onDelete: onDeleteTool })}
            {...(onToggleActive && { onToggleActive: onToggleActive })}
          />
        </div>
      ))}
    </ResponsiveGrid>
  );
};

export default ToolGrid;
