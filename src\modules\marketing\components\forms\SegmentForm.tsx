import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  FormItem,
  Input,
  Button,
  Select,
  Typography,
  IconCard,
  Icon,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { SegmentCondition, SegmentGroup, ConditionOperator, Segment, transformSegmentToFormValues } from '../../types/segment.types';
import { useFormErrors } from '@/shared/hooks/form';
import { useMarketingCustomFields } from '../../hooks/useMarketingCustomFieldQuery';
import { MarketingCustomFieldBusinessService } from '../../services/marketing-custom-field.service';
import { MarketingCustomFieldResponse } from '../../types/custom-field.types';

// Operators cho điều kiện - sẽ được translate trong component
const CONDITION_OPERATORS = [
  { value: ConditionOperator.EQUALS, labelKey: 'marketing:segment.form.operators.equals' },
  { value: ConditionOperator.NOT_EQUALS, labelKey: 'marketing:segment.form.operators.not_equals' },
  { value: ConditionOperator.CONTAINS, labelKey: 'marketing:segment.form.operators.contains' },
  { value: ConditionOperator.NOT_CONTAINS, labelKey: 'marketing:segment.form.operators.not_contains' },
  { value: ConditionOperator.GREATER_THAN, labelKey: 'marketing:segment.form.operators.greater_than' },
  { value: ConditionOperator.LESS_THAN, labelKey: 'marketing:segment.form.operators.less_than' },
  { value: ConditionOperator.IN, labelKey: 'marketing:segment.form.operators.greater_than_or_equal' },
  { value: ConditionOperator.NOT_IN, labelKey: 'marketing:segment.form.operators.less_than_or_equal' },
  { value: ConditionOperator.EXISTS, labelKey: 'marketing:segment.form.operators.is_not_empty' },
  { value: ConditionOperator.NOT_EXISTS, labelKey: 'marketing:segment.form.operators.is_empty' },
];

// Available fields cho điều kiện - sẽ được translate trong component
const AVAILABLE_FIELDS = [
  { value: 'email', labelKey: 'common:email' },
  { value: 'name', labelKey: 'common:name' },
  { value: 'phone', labelKey: 'common:phone' },
];

export interface SegmentFormValues {
  name: string;
  description?: string;
  groups: SegmentGroup[];
}

interface SegmentFormProps {
  initialData?: Segment | null | undefined;
  onSubmit: (values: SegmentFormValues) => void;
  onCancel: () => void;
}

/**
 * Component form thêm/sửa phân đoạn
 */
const SegmentForm: React.FC<SegmentFormProps> = ({ initialData, onSubmit, onCancel }) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { setFormErrors } = useFormErrors<SegmentFormValues>();

  // Fetch marketing custom fields (không sử dụng trực tiếp, chỉ để warm up cache)
  useMarketingCustomFields({});

  // Memoized translated operators
  const translatedOperators = useMemo(() =>
    CONDITION_OPERATORS.map(op => ({
      value: op.value,
      label: t(op.labelKey)
    })), [t]
  );

  // Memoized translated static fields
  const translatedStaticFields = useMemo(() =>
    AVAILABLE_FIELDS.map(field => ({
      value: field.value,
      label: t(field.labelKey),
      description: t('marketing:segment.form.systemField', 'Trường hệ thống')
    })), [t]
  );

  // Function để load cả static fields và custom fields - gọi API thật
  const loadAllFieldOptions = useCallback(async (params: { search?: string; page?: number; limit?: number }) => {
    console.log('loadAllFieldOptions called with params:', params);

    try {
      // Static fields (luôn hiển thị ở đầu)
      const staticFields = translatedStaticFields;

      // Nếu có search term, filter static fields
      const filteredStaticFields = params.search
        ? staticFields.filter(field =>
            field.label.toLowerCase().includes(params.search!.toLowerCase()) ||
            field.value.toLowerCase().includes(params.search!.toLowerCase())
          )
        : staticFields;

      // Gọi API để lấy custom fields với search và pagination
      const customFieldsResponse = await MarketingCustomFieldBusinessService.getCustomFieldsWithBusinessLogic({
        search: params.search,
        page: params.page || 1,
        limit: params.limit || 20,
      });

      // Transform custom fields response từ API structure
      const customFieldItems = customFieldsResponse.result.items.map((field: MarketingCustomFieldResponse) => ({
        value: field.fieldKey || field.id.toString(),
        label: field.displayName || 'Unnamed Field',
        description: field.description || 'Trường tùy chỉnh'
      }));

      console.log('Filtered static fields:', filteredStaticFields);
      console.log('Custom fields from API:', customFieldItems);

      // Combine static fields với custom fields từ API
      const allItems = [
        ...filteredStaticFields,
        ...customFieldItems
      ];

      // Tính toán pagination cho combined results
      const totalItems = filteredStaticFields.length + customFieldsResponse.result.meta.totalItems;
      const totalPages = Math.ceil(totalItems / (params.limit || 20));

      const result = {
        items: allItems,
        totalItems,
        totalPages,
        currentPage: params.page || 1
      };

      console.log('Final result:', result);
      return result;
    } catch (error) {
      console.error('Error loading field options:', error);
      // Fallback to static fields only
      const fallbackResult = {
        items: translatedStaticFields,
        totalItems: translatedStaticFields.length,
        totalPages: 1,
        currentPage: 1
      };
      console.log('Fallback result:', fallbackResult);
      return fallbackResult;
    }
  }, []);

  // State cho form data
  const [formData, setFormData] = useState<SegmentFormValues>(() => {
    if (initialData) {
      // Transform dữ liệu từ API response sang form values
      return transformSegmentToFormValues(initialData);
    }

    // Default form data cho tạo mới
    return {
      name: '',
      description: '',
      groups: [
        {
          id: `group-${Date.now()}`,
          conditions: [
            {
              id: `condition-${Date.now()}`,
              field: '',
              operator: ConditionOperator.EQUALS,
              value: '',
            },
          ],
          logicalOperator: 'AND',
        },
      ],
    };
  });

  // Thêm điều kiện mới vào group
  const addCondition = useCallback((groupId: string) => {
    setFormData(prev => ({
      ...prev,
      groups: prev.groups.map(group =>
        group.id === groupId
          ? {
              ...group,
              conditions: [
                ...group.conditions,
                {
                  id: `condition-${Date.now()}`,
                  field: '',
                  operator: ConditionOperator.EQUALS,
                  value: '',
                },
              ],
            }
          : group
      ),
    }));
  }, []);

  // Xóa điều kiện
  const removeCondition = useCallback((groupId: string, conditionId: string) => {
    setFormData(prev => ({
      ...prev,
      groups: prev.groups.map(group =>
        group.id === groupId
          ? {
              ...group,
              conditions: group.conditions.filter(condition => condition.id !== conditionId),
            }
          : group
      ),
    }));
  }, []);

  // Cập nhật điều kiện
  const updateCondition = useCallback((groupId: string, conditionId: string, updates: Partial<SegmentCondition>) => {
    setFormData(prev => ({
      ...prev,
      groups: prev.groups.map(group =>
        group.id === groupId
          ? {
              ...group,
              conditions: group.conditions.map(condition =>
                condition.id === conditionId ? { ...condition, ...updates } : condition
              ),
            }
          : group
      ),
    }));
  }, []);

  // Thêm nhóm điều kiện mới
  const addGroup = useCallback(() => {
    setFormData(prev => ({
      ...prev,
      groups: [
        ...prev.groups,
        {
          id: `group-${Date.now()}`,
          conditions: [
            {
              id: `condition-${Date.now()}`,
              field: '',
              operator: ConditionOperator.EQUALS,
              value: '',
            },
          ],
          logicalOperator: 'AND',
        },
      ],
    }));
  }, []);

  // Xóa nhóm điều kiện
  const removeGroup = useCallback((groupId: string) => {
    setFormData(prev => ({
      ...prev,
      groups: prev.groups.filter(group => group.id !== groupId),
    }));
  }, []);

  // Xử lý submit
  const handleSubmit = useCallback((event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Validation
    const errors: Record<string, string> = {};

    if (!formData['name']?.trim()) {
      errors['name'] = t('marketing:segment.form.validation.nameRequired');
    }

    // Validate groups
    if (formData['groups']?.length === 0) {
      errors['groups'] = t('marketing:segment.form.validation.conditionsRequired');
      setFormErrors(errors);
      return;
    }

    // Validate conditions in each group
    for (const group of formData['groups'] || []) {
      if (group.conditions.length === 0) {
        errors['groups'] = t('marketing:segment.form.validation.conditionsRequired');
        setFormErrors(errors);
        return;
      }

      for (const condition of group.conditions) {
        if (!condition.field || !condition.operator) {
          errors['groups'] = t('marketing:segment.form.validation.fieldRequired');
          setFormErrors(errors);
          return;
        }
      }
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    onSubmit(formData);
  }, [formData, onSubmit, setFormErrors]);

  return (
    <Card className="mb-4 p-4 overflow-visible">
      <Typography variant="h5" className="mb-4">
        {initialData ? t('marketing:segment.edit') : t('marketing:segment.addNew')}
      </Typography>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Tên Segment */}
        <FormItem label={t('marketing:segment.form.name')} name="name" required>
          <Input
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder={t('marketing:segment.form.namePlaceholder')}
            fullWidth
          />
        </FormItem>

        {/* Mô tả */}
        <FormItem label={t('marketing:segment.form.description')} name="description">
          <Input
            value={formData.description || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder={t('marketing:segment.form.descriptionPlaceholder')}
            fullWidth
          />
        </FormItem>

        {/* Điều kiện */}
        <div className="space-y-4 overflow-visible">
          <Typography variant="h6">{t('marketing:segment.form.conditions')}</Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {t('marketing:segment.form.conditionsDescription')}
          </Typography>

          {formData.groups.map((group, groupIndex) => (
            <div key={group.id} className="bg-card rounded-lg p-4 space-y-4 overflow-visible">
              {/* Header nhóm điều kiện */}
              <div className="flex items-center justify-between">
                <Typography variant="body2" className="text-muted-foreground">
                  {groupIndex > 0 && (
                    <span className="mr-2 px-2 py-1 bg-primary/10 text-primary rounded text-xs font-medium">
                      {t('marketing:segment.form.or')}
                    </span>
                  )}
                  {t('marketing:segment.form.conditionsDescription')}
                </Typography>

                {formData.groups.length > 1 && (
                  <IconCard
                    icon="x"
                    size="sm"
                    variant="default"
                    onClick={() => removeGroup(group.id)}
                    className="text-destructive hover:text-destructive/80"
                  />
                )}
              </div>



              {/* Điều kiện trong nhóm */}
              <div className="space-y-3 overflow-visible">
                {group.conditions.map((condition, conditionIndex) => (
                  <div key={condition.id} className="grid grid-cols-12 gap-3 items-center overflow-visible">
                    {/* Cột VÀ - cố định width */}
                    <div className="col-span-1 flex justify-center">
                      {conditionIndex > 0 && (
                        <Typography variant="body2" className="text-muted-foreground font-medium">
                          {t('marketing:segment.form.and')}
                        </Typography>
                      )}
                    </div>

                    {/* Field selector */}
                    <div className="col-span-12 md:col-span-3 relative mb-2 md:mb-0">
                      <AsyncSelectWithPagination
                        value={condition.field}
                        onChange={(value) => updateCondition(group.id, condition.id, { field: value as string })}
                        loadOptions={loadAllFieldOptions}
                        placeholder={t('marketing:segment.selectCustomField', 'Chọn trường...')}
                        searchOnEnter={true}
                        autoLoadInitial={true}
                        debounceTime={300}
                        itemsPerPage={20}
                        noOptionsMessage={t('common:noOptionsFound', 'Không tìm thấy tùy chọn')}
                        loadingMessage={t('common:loading', 'Đang tải...')}
                        fullWidth
                      />
                    </div>

                    {/* Operator selector */}
                    <div className="col-span-12 md:col-span-3 relative mb-2 md:mb-0">
                      <Select
                        value={condition.operator}
                        onChange={(value) => updateCondition(group.id, condition.id, { operator: value as ConditionOperator })}
                        options={translatedOperators}
                        placeholder={t('marketing:segment.form.operator')}
                        fullWidth
                      />
                    </div>

                    {/* Value input */}
                    <div className="col-span-12 md:col-span-4">
                      <Input
                        value={condition.value as string}
                        onChange={(e) => updateCondition(group.id, condition.id, { value: e.target.value })}
                        placeholder={t('marketing:segment.form.valuePlaceholder')}
                        fullWidth
                      />
                    </div>

                    {/* Xóa điều kiện */}
                    <div className="col-span-1 flex justify-center">
                      {group.conditions.length > 1 && (
                        <IconCard
                          icon="trash"
                          size="sm"
                          variant="default"
                          onClick={() => removeCondition(group.id, condition.id)}
                          className="text-destructive hover:text-destructive/80"
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Thêm điều kiện */}
              <div className="flex justify-start">
                <Button
                  type="button"
                  variant="primary"
                  size="sm"
                  onClick={() => addCondition(group.id)}
                  leftIcon={<Icon name="plus" size="sm" />}
                >
                  {t('marketing:segment.form.addCondition')}
                </Button>
              </div>
            </div>
          ))}

          {/* Thêm nhóm điều kiện */}
          <div className="flex justify-start">
            <Button
              type="button"
              variant="primary"
              size="sm"
              onClick={addGroup}
              leftIcon={<Icon name="plus" size="sm" />}
            >
              {t('marketing:segment.form.addGroup')}
            </Button>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t border-border">
          <IconCard
            icon="x"
            title={t('marketing:segment.form.buttons.cancel')}
            variant="secondary"
            onClick={onCancel}
            className="cursor-pointer"
          />
          <IconCard
            icon="check"
            title={t('marketing:segment.form.buttons.save')}
            variant="primary"
            onClick={() => {
              const form = document.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }}
            className="cursor-pointer"
          />
        </div>
      </form>
    </Card>
  );
};

export default SegmentForm;