import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Controller, type FieldValues } from 'react-hook-form';

import {
  Form,
  FormItem,
  Input,
  Button,
  Checkbox,
  Alert,
  PasswordInput,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { createAdminLoginSchema, AdminLoginFormValues } from '../schemas/admin-auth.schema';
import { AdminLoginRequest, AdminLoginResponse } from '../types/admin-auth.types';
import { useFormErrors } from '@/shared/hooks';

import { useAdminLogin } from '../hooks/useAdminAuthQuery';
import { useAuthCommon } from '@/shared/hooks/useAuthCommon';
import { useRecaptcha } from '../../../auth/hooks/useRecaptcha';
import {
  saveCredentials,
  getCredentials,
  clearCredentials,
  areCredentialsValid,
  isRememberMeChecked,
} from '../utils/admin-auth-storage.utils';
// import { env } from '@/shared/utils'; // Unused import

interface AdminLoginFormProps {
  onSuccess?: () => void;
  onForgotPassword?: () => void;
}

/**
 * Admin Recaptcha Component
 */

/**
 * Admin login form component
 */
const AdminLoginForm: React.FC<AdminLoginFormProps> = ({ onSuccess, onForgotPassword }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { setAdminAuth, clearAuth } = useAuthCommon();
  const { mutate: login, isPending } = useAdminLogin();
  const { formRef, setFormErrors } = useFormErrors<AdminLoginFormValues>();

  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Sử dụng hook useRecaptcha để quản lý reCAPTCHA
  const {
    recaptchaToken,
    error: recaptchaError,
    resetRecaptcha,
  } = useRecaptcha('admin-recaptcha-container', 'ADMIN_LOGIN');

  // Hiển thị lỗi reCAPTCHA nếu có
  useEffect(() => {
    if (recaptchaError) {
      setErrorMessage(t('auth:recaptchaError', 'Lỗi xác thực reCAPTCHA: ') + recaptchaError);
    }
  }, [recaptchaError, t]);

  // Create login schema with translations
  const loginSchema = createAdminLoginSchema(t);

  // Lưu trữ thông tin đăng nhập đã lấy được
  const [savedUsername, setSavedUsername] = useState<string>('');
  const [savedPassword, setSavedPassword] = useState<string>('');
  const [rememberMeChecked, setRememberMeChecked] = useState<boolean>(false);
  // Theo dõi xem người dùng đã thay đổi giá trị trong form chưa
  const [formEdited, setFormEdited] = useState<boolean>(false);

  useEffect(() => {
    clearAuth();
  }, [clearAuth]);

  // Check for saved credentials on component mount
  useEffect(() => {
    try {
      // Kiểm tra xem người dùng đã chọn "Remember me" chưa và thông tin đăng nhập có hợp lệ không
      const isValid = areCredentialsValid();

      // Lấy trạng thái Remember me
      const isRememberMe = isRememberMeChecked();
      setRememberMeChecked(isRememberMe);

      if (isValid) {
        const savedCredentials = getCredentials();

        if (savedCredentials && savedCredentials.username) {
          // Lưu thông tin đăng nhập để sử dụng trong form
          setSavedUsername(savedCredentials.username);
          setSavedPassword(savedCredentials.password);
        } else {
          clearCredentials();
        }
      } else {
        // Clear expired credentials or if Remember me is not checked
        clearCredentials();
      }
    } catch (error) {
      console.error('Error checking saved credentials:', error);
      clearCredentials();
    }
  }, []);

  // Lưu trữ giá trị hiện tại của form
  const [currentUsername, setCurrentUsername] = useState<string>(savedUsername);
  const [currentPassword, setCurrentPassword] = useState<string>(savedPassword);
  const [currentRememberMe, setCurrentRememberMe] = useState<boolean>(rememberMeChecked);

  // Theo dõi xem reCAPTCHA đã được xác thực chưa
  useEffect(() => {
    // Khi recaptchaToken thay đổi (đã xác thực), đánh dấu form đã được chỉnh sửa
    // nhưng không làm mất trạng thái của các trường đã nhập
    if (recaptchaToken) {
      setFormEdited(true);
    }
  }, [recaptchaToken]);

  // Khi savedUsername, savedPassword hoặc rememberMeChecked thay đổi và form chưa được chỉnh sửa
  useEffect(() => {
    if (!formEdited) {
      setCurrentUsername(savedUsername);
      setCurrentPassword(savedPassword);
      setCurrentRememberMe(rememberMeChecked);
    }
  }, [savedUsername, savedPassword, rememberMeChecked, formEdited]);

  // Handle form submission
  const handleSubmit = (values: unknown) => {
    // Use type assertion with a specific type instead of 'any'
    const loginValues = values as AdminLoginFormValues;

    // Đánh dấu form đã được chỉnh sửa để tránh quay lại giá trị đã lưu
    setFormEdited(true);

    // Save credentials if remember me is checked
    if (loginValues.rememberMe) {
      if (loginValues.email && loginValues.password) {
        // Lưu thông tin đăng nhập
        saveCredentials(loginValues.email, loginValues.password);
      }
    } else {
      clearCredentials();
    }

    // Reset error message
    setErrorMessage(null);

    // Reset form errors
    setFormErrors({});

    // Kiểm tra xem reCAPTCHA đã được xác thực chưa
    if (!recaptchaToken) {
      setFormErrors({
        email: t('auth:recaptchaRequired', 'Vui lòng xác thực reCAPTCHA trước khi đăng nhập'),
      });
      return;
    }

    // Call login API with admin role
    login(
      {
        email: loginValues.email,
        password: loginValues.password,
        recaptchaToken: recaptchaToken || undefined,
        // Trong thực tế, cần mở rộng API để hỗ trợ role admin
        // Ở đây giả định API đã hỗ trợ
      } as AdminLoginRequest,
      {
        onSuccess: (response: ApiResponseDto<unknown>) => {
          // Reset reCAPTCHA sau khi đăng nhập thành công
          resetRecaptcha();

          // Lưu token vào Redux store thông qua hook useAdminAuth
          if (
            response.result &&
            typeof response.result === 'object' &&
            'accessToken' in response.result
          ) {
            const result = response.result as AdminLoginResponse;
            if (result.accessToken) {
              // Lưu thông tin đăng nhập vào Redux
              setAdminAuth({
                accessToken: result.accessToken,
                expiresIn: result.expiresIn || 0,
                expiresAt: result.expiresAt || Date.now() + (result.expiresIn || 0) * 1000,
                employee: result.employee,
              });
            }
          }

          // Đăng nhập thành công
          // Call success callback if provided
          if (onSuccess) {
            onSuccess();
          }

          // Navigate to admin dashboard
          navigate('/admin');
        },
        onError: (error: unknown) => {
          console.error('Admin login error:', error);

          // Reset lỗi form trước khi xử lý lỗi mới
          setFormErrors({});

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('adminAuth:auth.loginError', 'Đăng nhập quản trị thất bại');

          // Kiểm tra xem error có phải là AxiosError không
          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as {
              response: { data?: { code?: number; message?: string } };
            };

            if (axiosError.response.data?.message) {
              errorMsg = axiosError.response.data.message;

              // Xử lý lỗi code 10011 - Email hoặc mật khẩu không chính xác
              if (axiosError.response.data?.code === 10011) {
                // Hiển thị lỗi trong cả hai trường email và mật khẩu
                setFormErrors({
                  email: t(
                    'adminAuth:auth.invalidCredentials',
                    'Email hoặc mật khẩu không chính xác'
                  ),
                  password: t(
                    'adminAuth:auth.invalidCredentials',
                    'Email hoặc mật khẩu không chính xác'
                  ),
                });

                // Không hiển thị thông báo lỗi chung nữa vì đã hiển thị lỗi trong các trường
                setErrorMessage(null);
              } else {
                // Hiển thị thông báo lỗi chung cho các lỗi khác
                setErrorMessage(errorMsg);
              }
            } else {
              setErrorMessage(errorMsg);
            }
          } else {
            setErrorMessage(errorMsg);
          }

          // Reset reCAPTCHA sau khi đăng nhập thất bại
          resetRecaptcha();

          // Giữ trạng thái form đã chỉnh sửa để không quay lại giá trị đã lưu
          setFormEdited(true);
        },
      }
    );
  };

  return (
    <div className="space-y-4">
      {errorMessage && <Alert type="error" message={errorMessage} className="mb-4" />}
      <Form
        ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>}
        schema={loginSchema}
        onSubmit={handleSubmit}
        className="space-y-4"
        autoComplete="off"
        defaultValues={{
          email: currentUsername,
          password: currentPassword,
          rememberMe: currentRememberMe,
        }}
      >
        <FormItem name="email" label={t('auth:email')} required>
          <Input
            type="email"
            fullWidth
            autoComplete="off"
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              // Cập nhật giá trị hiện tại
              setCurrentUsername(e.target.value);
              // Đánh dấu form đã được chỉnh sửa
              if (!formEdited) setFormEdited(true);
            }}
          />
        </FormItem>

        <FormItem name="password" label={t('auth:password')} required>
          <PasswordInput
            fullWidth
            autoComplete="new-password"
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              // Cập nhật giá trị hiện tại
              setCurrentPassword(e.target.value);
              // Đánh dấu form đã được chỉnh sửa
              if (!formEdited) setFormEdited(true);
            }}
          />
        </FormItem>

        <div className="flex justify-between items-start mb-4">
          <Controller
            name="rememberMe"
            render={({ field }) => (
              <div className="flex items-center">
                <Checkbox
                  checked={field.value}
                  onChange={checked => {
                    // Cập nhật giá trị trong react-hook-form
                    field.onChange(checked);
                    // Cập nhật state để lưu trữ giá trị hiện tại
                    setCurrentRememberMe(checked);
                    // Đánh dấu form đã được chỉnh sửa
                    if (!formEdited) setFormEdited(true);
                  }}
                  label={t('auth.rememberMe')}
                  variant="filled"
                  color="primary"
                  size="md"
                />
              </div>
            )}
          />

          <a
            href="#"
            onClick={e => {
              e.preventDefault();
              onForgotPassword?.();
            }}
            className="text-primary hover:text-primary/80 text-sm font-medium"
          >
            {t('auth:forgotPassword')}
          </a>
        </div>
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300"></div>
          </div>

          {/* Container cho reCAPTCHA */}
          <div
            id="admin-recaptcha-container"
            className="flex justify-center min-h-[78px] dark:border-gray-700 rounded-md"
          ></div>
        </div>
        <Button type="submit" variant="primary" fullWidth isLoading={isPending}>
          {t('adminAuth:auth.signIn')}
        </Button>
      </Form>
    </div>
  );
};

export default AdminLoginForm;
