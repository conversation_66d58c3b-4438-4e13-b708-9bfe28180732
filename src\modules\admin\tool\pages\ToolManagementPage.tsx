import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý Tools
 */
const ToolManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin-tool']);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Tools Card */}
        <ModuleCard
          title={t('admin-tool:tools', 'Tools')}
          description={t(
            'admin-tool:toolsDescription',
            'Quản lý các tools trong hệ thống, bao gồm thêm, sử<PERSON>, xóa và phê duyệt tools.'
          )}
          icon="settings"
          linkTo="/admin/tools/list"
        />
        <ModuleCard
          title={t('admin-tool:trash.titleTrash', 'Thùng rác')}
          description={t(
            'admin-tool:trash.trashDescription',
            'Quản lý các tools đã xóa mềm, bao gồm khôi phục và xóa vĩnh viễn tools.'
          )}
          icon="trash"
          linkTo="/admin/tools/trash"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default ToolManagementPage;
