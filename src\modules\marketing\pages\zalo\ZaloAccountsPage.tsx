import React, { useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Card,
  Table,
  Chip,
  IconCard,
  Tooltip,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useZaloAccounts } from '../../hooks/zalo/useZaloAccounts';
import { ConnectZaloAccountForm } from '../../components/zalo/ConnectZaloAccountForm';
import type { ZaloOAAccountDto, ZaloOAAccountQueryDto } from '../../types/zalo.types';
import { ZaloOAStatus } from '../../types/zalo.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { TableColumn } from '@/shared/components/common/Table/types';

/**
 * Trang quản lý Zalo OA Accounts
 */
export function ZaloAccountsPage() {
  const { t } = useTranslation('marketing');
  const [searchParams, setSearchParams] = useSearchParams();

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Kiểm tra nếu có action=connect trong URL thì hiển thị form
  useEffect(() => {
    if (searchParams.get('action') === 'connect') {
      showForm();
      setSearchParams({});
    }
  }, [searchParams, showForm, setSearchParams]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<ZaloOAAccountDto>[]>(
    () => [
      {
        key: 'name',
        title: t('marketing:zalo.accounts.table.name', 'Tên OA'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown, record: ZaloOAAccountDto) => (
          <div className="flex items-center space-x-3">
            <div className="h-8 w-8 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white font-semibold text-sm">
              {record.name.charAt(0).toUpperCase()}
            </div>
            <span className="font-medium">{String(value || '')}</span>
          </div>
        ),
      },
      {
        key: 'oaId',
        title: t('marketing:zalo.accounts.table.oaId', 'OA ID'),
        dataIndex: 'oaId',
        sortable: true,
        render: (value: unknown) => (
          <span className="font-mono text-sm">{String(value || '')}</span>
        ),
      },
      {
        key: 'followersCount',
        title: t('marketing:zalo.accounts.table.followers', 'Followers'),
        dataIndex: 'followersCount',
        sortable: true,
        render: (value: unknown) => (
          <span className="font-medium">{Number(value || 0).toLocaleString()}</span>
        ),
      },
      {
        key: 'status',
        title: t('marketing:zalo.accounts.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (value: unknown) => (
          <Chip
            variant={value === ZaloOAStatus.ACTIVE ? 'success' : 'warning'}
          >
            {value === ZaloOAStatus.ACTIVE ? t('common.status.active', 'Hoạt động') : t('common.status.inactive', 'Không hoạt động')}
          </Chip>
        ),
      },
      {
        key: 'updatedAt',
        title: t('marketing:zalo.accounts.table.lastUpdate', 'Cập nhật cuối'),
        dataIndex: 'updatedAt',
        sortable: true,
        render: (value: unknown) => (
          <span className="text-sm text-muted-foreground">
            {value ? new Date(Number(value)).toLocaleDateString('vi-VN') : ''}
          </span>
        ),
      },
      {
        key: 'actions',
        title: t('marketing:zalo.accounts.table.actions', 'Thao tác'),
        width: '120px',
        render: (_: unknown, record: ZaloOAAccountDto) => (
          <div className="flex items-center space-x-2">
            <Tooltip content={t('common.edit', 'Chỉnh sửa')}>
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => {
                  console.log('Edit account:', record.id);
                  // Handle edit
                }}
              />
            </Tooltip>
            <Tooltip content={t('common.delete', 'Xóa')}>
              <IconCard
                icon="trash"
                variant="danger"
                size="sm"
                onClick={() => {
                  console.log('Delete account:', record.id);
                  // Handle delete
                }}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t]
  );

  // Tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'active',
        label: t('common.status.active', 'Hoạt động'),
        icon: 'check',
        value: ZaloOAStatus.ACTIVE,
      },
      {
        id: 'inactive',
        label: t('common.status.inactive', 'Không hoạt động'),
        icon: 'x',
        value: ZaloOAStatus.INACTIVE,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): ZaloOAAccountQueryDto => {
      const queryParams: ZaloOAAccountQueryDto = {
        page: params.page,
        limit: params.pageSize,
      };

      // Only add optional properties if they have values
      if (params.searchTerm) {
        queryParams.search = params.searchTerm;
      }
      if (params.sortBy) {
        queryParams.sortBy = params.sortBy;
      }
      if (params.sortDirection) {
        queryParams.sortDirection = params.sortDirection;
      }

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as ZaloOAStatus;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<ZaloOAAccountDto, ZaloOAAccountQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Hooks để gọi API
  const { data: accountsData, isLoading } = useZaloAccounts(dataTable.queryParams);

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng khi có dữ liệu từ API
  useEffect(() => {
    if (accountsData && accountsData.meta) {
      updateTableDataRef.current(accountsData, isLoading);
    }
  }, [accountsData, isLoading]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        [ZaloOAStatus.ACTIVE]: t('common.status.active', 'Hoạt động'),
        [ZaloOAStatus.INACTIVE]: t('common.status.inactive', 'Không hoạt động'),
      },
      t,
    });

  const handleConnectSuccess = () => {
    hideForm();
    setSearchParams({});
  };

  return (
    <div className="w-full bg-background text-foreground space-y-4">
      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={() => showForm()}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Accounts Table */}
      <Card className="overflow-hidden">
        <Table<ZaloOAAccountDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={accountsData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: accountsData?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: accountsData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* SlideInForm cho form kết nối OA */}
      <SlideInForm isVisible={isVisible}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">
              {t('marketing:zalo.accounts.connect.title', 'Kết nối Zalo OA')}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              {t('marketing:zalo.accounts.connect.description', 'Nhập thông tin để kết nối Zalo Official Account')}
            </p>
          </div>
          <ConnectZaloAccountForm onSuccess={handleConnectSuccess} onCancel={hideForm} />
        </div>
      </SlideInForm>
    </div>
  );
}

export default ZaloAccountsPage;
