import React, { useState, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import { ActiveFilters } from '@/modules/components/filters';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, Typography } from '@/shared/components/common';

// Types cho integration categories và providers
interface IntegrationItem {
  id: string;
  title: string;
  description: string;
  icon: string;
  linkTo: string;
  category: IntegrationCategory;
  gradientColor?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
}

type IntegrationCategory = 'bank' | 'model' | 'sms' | 'email' | 'database' | 'social' | 'other';

/**
 * Trang tổng quan quản lý tích hợp cho Admin
 */
const UserIntegrationManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common', 'integration']);
  const navigate = useNavigate();

  // State cho search và filter
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<IntegrationCategory | 'all'>('all');

  // Refs cho scroll đến từng section
  const sectionRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Filter categories cho MenuIconBar
  const filterCategories = [
    { id: 'all', label: t('common:all'), icon: 'list' as const, onClick: () => handleCategoryClick('all') },
    { id: 'bank', label: 'Tài khoản ngân hàng', icon: 'bank' as const, onClick: () => handleCategoryClick('bank') },
    { id: 'model', label: 'Model', icon: 'openai-red' as const, onClick: () => handleCategoryClick('model') },
    { id: 'sms', label: 'SMS', icon: 'mail-plus' as const, onClick: () => handleCategoryClick('sms') },
    { id: 'email', label: 'Email', icon: 'mail' as const, onClick: () => handleCategoryClick('email') },
    { id: 'database', label: 'Database', icon: 'database' as const, onClick: () => handleCategoryClick('database') },
    { id: 'social', label: 'Mạng xã hội', icon: 'users' as const, onClick: () => handleCategoryClick('social') },
    { id: 'other', label: 'Khác', icon: 'more-horizontal' as const, onClick: () => handleCategoryClick('other') }
  ];

  // Danh sách tất cả integrations
  const allIntegrations: IntegrationItem[] = useMemo(() => [
    // Banking integrations
    {
      id: 'mb-banking',
      title: 'MB Banking',
      description: 'Tích hợp với ngân hàng MB Bank (Military Bank)',
      icon: 'bank',
      linkTo: '/integrations/banking/mb',
      category: 'bank',
      gradientColor: 'success'
    },
    {
      id: 'acb-banking',
      title: 'ACB Banking',
      description: 'Tích hợp với ngân hàng ACB (Asia Commercial Bank)',
      icon: 'bank',
      linkTo: '/integrations/banking/acb',
      category: 'bank',
      gradientColor: 'primary'
    },
    {
      id: 'ocb-banking',
      title: 'OCB Banking',
      description: 'Tích hợp với ngân hàng OCB (Orient Commercial Bank)',
      icon: 'bank',
      linkTo: '/integrations/banking/ocb',
      category: 'bank',
      gradientColor: 'info'
    },
    // Email providers
    {
      id: 'smtp',
      title: 'SMTP',
      description: 'Cấu hình máy chủ SMTP cho gửi email',
      icon: 'mail',
      linkTo: '/integrations/email/smtp',
      category: 'email',
      gradientColor: 'primary'
    },
    {
      id: 'outlook',
      title: 'Outlook',
      description: 'Tích hợp với Microsoft Outlook',
      icon: 'mail',
      linkTo: '/integrations/email/outlook',
      category: 'email',
      gradientColor: 'info'
    },
    {
      id: 'yahoo-mail',
      title: 'Yahoo Mail',
      description: 'Tích hợp với Yahoo Mail',
      icon: 'mail',
      linkTo: '/integrations/email/yahoo',
      category: 'email',
      gradientColor: 'warning'
    },
    {
      id: 'sendgrid',
      title: 'SendGrid',
      description: 'Dịch vụ email marketing SendGrid',
      icon: 'mail',
      linkTo: '/integrations/email/sendgrid',
      category: 'email',
      gradientColor: 'primary'
    },
    {
      id: 'mailchimp',
      title: 'Mailchimp Transactional',
      description: 'Email transactional qua Mailchimp',
      icon: 'mail',
      linkTo: '/integrations/email/mailchimp',
      category: 'email',
      gradientColor: 'warning'
    },
    {
      id: 'amazon-ses',
      title: 'Amazon SES',
      description: 'Amazon Simple Email Service',
      icon: 'mail',
      linkTo: '/integrations/email/amazon-ses',
      category: 'email',
      gradientColor: 'warning'
    },
    {
      id: 'mailgun',
      title: 'Mailgun',
      description: 'Dịch vụ email API Mailgun',
      icon: 'mail',
      linkTo: '/integrations/email/mailgun',
      category: 'email',
      gradientColor: 'error'
    },
    {
      id: 'gmail',
      title: 'Gmail',
      description: 'Tích hợp với Gmail API',
      icon: 'mail',
      linkTo: '/integrations/email/gmail',
      category: 'email',
      gradientColor: 'error'
    },
    // SMS providers
    {
      id: 'twilio',
      title: 'Twilio',
      description: 'Dịch vụ SMS quốc tế Twilio',
      icon: 'mail-plus',
      linkTo: '/integrations/sms/twilio',
      category: 'sms',
      gradientColor: 'error'
    },
    {
      id: 'ftp-sms',
      title: 'FTP SMS Brandname',
      description: 'Dịch vụ SMS brandname FTP',
      icon: 'mail-plus',
      linkTo: '/integrations/sms/ftp',
      category: 'sms',
      gradientColor: 'primary'
    },
    {
      id: 'vnpt-sms',
      title: 'VNPT SMS',
      description: 'Dịch vụ SMS VNPT',
      icon: 'mail-plus',
      linkTo: '/integrations/sms/vnpt',
      category: 'sms',
      gradientColor: 'info'
    },
    // Existing integrations
    {
      id: 'email-server',
      title: t('admin:integration.email.title', 'Quản lý Email'),
      description: t(
        'admin:integration.email.description',
        'Quản lý cấu hình máy chủ email cho hệ thống gửi email tự động'
      ),
      icon: 'mail',
      linkTo: '/integrations/email',
      category: 'email'
    },
    {
      id: 'facebook',
      title: t('admin:integration.facebook.title', 'Quản lý Facebook'),
      description: t('admin:integration.facebook.description', 'Quản lý tích hợp với Facebook'),
      icon: 'facebook',
      linkTo: '/integrations/facebook',
      category: 'other'
    },
    {
      id: 'website',
      title: t('admin:integration.website.title', 'Quản lý Website'),
      description: t(
        'admin:integration.website.description',
        'Quản lý tích hợp với các trang web'
      ),
      icon: 'website',
      linkTo: '/integrations/website',
      category: 'other'
    },
    {
      id: 'bank-accounts',
      title: t('integration:bankAccounts.title', 'Quản lý Tài khoản ngân hàng'),
      description: t(
        'integration:bankAccount.description',
        'Quản lý tích hợp với các tài khoản ngân hàng'
      ),
      icon: 'bank',
      linkTo: '/user/bank-accounts',
      category: 'bank'
    },
    {
      id: 'sms-integration',
      title: t('integration:sms.title', 'Quản lý SMS'),
      description: t(
        'integration:sms.description',
        'Quản lý tích hợp các nhà cung cấp dịch vụ SMS'
      ),
      icon: 'mail-plus',
      linkTo: '/integrations/sms',
      category: 'sms'
    },
    {
      id: 'database',
      title: t('admin:integration.database.title', 'Quản lý Database Integration'),
      description: t(
        'admin:integration.database.description',
        'Quản lý kết nối database cho hệ thống tích hợp dữ liệu'
      ),
      icon: 'database',
      linkTo: '/integrations/database',
      category: 'database'
    },
    // AI/ML Model providers
    {
      id: 'openai',
      title: 'OpenAI',
      description: 'Tích hợp với OpenAI GPT-4, GPT-3.5, DALL-E và các model AI khác',
      icon: 'openai-red',
      linkTo: '/integrations/ai/openai',
      category: 'model',
      gradientColor: 'success'
    },
    {
      id: 'anthropic',
      title: 'Anthropic',
      description: 'Tích hợp với Anthropic Claude và các model AI conversation',
      icon: 'openai-red',
      linkTo: '/integrations/ai/anthropic',
      category: 'model',
      gradientColor: 'warning'
    },
    {
      id: 'gemini',
      title: 'Google Gemini',
      description: 'Tích hợp với Google Gemini Pro và các model AI của Google',
      icon: 'openai-red',
      linkTo: '/integrations/ai/gemini',
      category: 'model',
      gradientColor: 'primary'
    },
    {
      id: 'deepseek',
      title: 'DeepSeek',
      description: 'Tích hợp với DeepSeek AI models và deep learning services',
      icon: 'openai-red',
      linkTo: '/integrations/ai/deepseek',
      category: 'model',
      gradientColor: 'info'
    },
    {
      id: 'provider-model',
      title: t('integration:providerModel.title', 'Quản lý Provider Model'),
      description: t(
        'integration:ai.description',
        'Quản lý các nhà cung cấp AI và cấu hình kết nối'
      ),
      icon: 'openai-red',
      linkTo: '/integrations/provider-model',
      category: 'model',
      gradientColor: 'secondary'
    },
    // Social integrations
    {
      id: 'zalo-oa',
      title: 'Zalo OA',
      description: 'Tích hợp với Zalo Official Account để gửi tin nhắn và quản lý khách hàng',
      icon: 'users',
      linkTo: '/integrations/social/zalo-oa',
      category: 'social',
      gradientColor: 'info'
    },

    {
      id: 'google-calendar',
      title: t('integration:calendar.title', 'Quản lý Google Calendar'),
      description: t(
        'integration:calendar.description',
        'Tích hợp và đồng bộ lịch với Google Calendar'
      ),
      icon: 'calendar',
      linkTo: '/integrations/google-calendar',
      category: 'other'
    },
    {
      id: 'shipping',
      title: t('integration:shipping.title', 'Quản lý Vận chuyển'),
      description: t(
        'integration:shipping.description',
        'Tích hợp với các nhà vận chuyển GHN, GHTK, Viettel Post'
      ),
      icon: 'truck',
      linkTo: '/integrations/shipping',
      category: 'other'
    },
    {
      id: 'cloud-storage',
      title: t('integration:cloudStorage.title', 'Quản lý Cloud Storage'),
      description: t(
        'integration:cloudStorage.description',
        'Tích hợp với Google Drive, OneDrive, Dropbox'
      ),
      icon: 'cloud',
      linkTo: '/integrations/cloud-storage',
      category: 'other'
    },
    {
      id: 'enterprise-storage',
      title: t('integration:enterpriseStorage.title', 'Quản lý Enterprise Storage'),
      description: t(
        'integration:enterpriseStorage.description',
        'Tích hợp với AWS S3, Azure Blob Storage'
      ),
      icon: 'server',
      linkTo: '/integrations/enterprise-storage',
      category: 'other'
    },
    {
      id: 'external-agents',
      title: t('integration:externalAgents.title', 'Quản lý External Agents'),
      description: t(
        'integration:externalAgents.description',
        'Tích hợp với các external agents thông qua MCP, REST API, WebSocket'
      ),
      icon: 'users',
      linkTo: '/integrations/external-agents',
      category: 'other'
    }
  ], [t]);

  // Nhóm integrations theo category
  const integrationsByCategory = useMemo(() => {
    const categories: { [key in IntegrationCategory]: IntegrationItem[] } = {
      bank: [],
      model: [],
      sms: [],
      email: [],
      database: [],
      social: [],
      other: []
    };

    allIntegrations.forEach(integration => {
      categories[integration.category].push(integration);
    });

    return categories;
  }, [allIntegrations]);

  // Lọc integrations theo search term
  const filteredIntegrationsByCategory = useMemo(() => {
    if (!searchTerm.trim()) {
      return integrationsByCategory;
    }

    const filtered: { [key in IntegrationCategory]: IntegrationItem[] } = {
      bank: [],
      model: [],
      sms: [],
      email: [],
      database: [],
      social: [],
      other: []
    };

    Object.entries(integrationsByCategory).forEach(([category, integrations]) => {
      filtered[category as IntegrationCategory] = integrations.filter(integration =>
        integration.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        integration.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    });

    return filtered;
  }, [integrationsByCategory, searchTerm]);

  // Handlers
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
  };

  const handleClearFilter = () => {
    setSelectedCategory('all');
  };

  const handleClearAll = () => {
    setSearchTerm('');
    setSelectedCategory('all');
  };

  // Handler để điều hướng đến trang tích hợp của tôi
  const handleGoToMyIntegrations = () => {
    navigate('/integrations/my-integrations');
  };

  const handleCategoryClick = (category: IntegrationCategory | 'all') => {
    setSelectedCategory(category);

    // Scroll đến section tương ứng
    if (category !== 'all') {
      const sectionElement = sectionRefs.current[category];
      if (sectionElement) {
        sectionElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }
    } else {
      // Scroll về đầu trang khi chọn "Tất cả"
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // Get filter label for ActiveFilters
  const getFilterLabel = () => {
    const category = filterCategories.find(cat => cat.id === selectedCategory);
    return category?.label || '';
  };

  // Set ref cho section
  const setSectionRef = (category: string) => (el: HTMLDivElement | null) => {
    sectionRefs.current[category] = el;
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* MenuIconBar */}
        <MenuIconBar
          onSearch={handleSearch}
          items={filterCategories}
          showDateFilter={false}
          showColumnFilter={false}
          additionalIcons={[
            {
              icon: 'list',
              tooltip: t('integration:myIntegrations', 'Tích hợp của tôi'),
              variant: 'primary',
              onClick: handleGoToMyIntegrations,
              className: 'shadow-md',
            },
          ]}
        />

        {/* Active Filters */}
        <ActiveFilters
          searchTerm={searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={selectedCategory !== 'all' ? selectedCategory : undefined}
          filterLabel={selectedCategory !== 'all' ? getFilterLabel() : undefined}
          onClearFilter={handleClearFilter}
          onClearAll={handleClearAll}
        />

        {/* Integration Sections */}
        <div className="space-y-8">
          {Object.entries(filteredIntegrationsByCategory).map(([category, integrations]) => {
            // Chỉ hiển thị category nếu có integrations hoặc không có search term
            if (integrations.length === 0 && searchTerm.trim()) {
              return null;
            }

            // Nếu có selectedCategory và không phải 'all', chỉ hiển thị category được chọn
            if (selectedCategory !== 'all' && selectedCategory !== category) {
              return null;
            }

            const categoryInfo = filterCategories.find(cat => cat.id === category);

            return (
              <div
                key={category}
                ref={setSectionRef(category)}
                className="scroll-mt-24" // Offset cho sticky header
              >
                {/* Category Title */}
                <div className="mb-6">
                  <Typography variant="h2" className="text-2xl font-bold mb-2 flex items-center gap-3">
                    {categoryInfo && (
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <span className="text-primary text-lg">
                          {categoryInfo.icon === 'bank' && '🏦'}
                          {categoryInfo.icon === 'mail' && '📧'}
                          {categoryInfo.icon === 'mail-plus' && '📱'}
                          {categoryInfo.icon === 'database' && '🗄️'}
                          {categoryInfo.icon === 'openai-red' && '🤖'}
                          {categoryInfo.icon === 'users' && '👥'}
                          {categoryInfo.icon === 'more-horizontal' && '🔧'}
                        </span>
                      </div>
                    )}
                    {categoryInfo?.label || category}
                  </Typography>
                  <Typography variant="body1" className="text-muted-foreground">
                    {category === 'bank' && 'Tích hợp với các ngân hàng và dịch vụ tài chính'}
                    {category === 'email' && 'Tích hợp với các nhà cung cấp dịch vụ email'}
                    {category === 'sms' && 'Tích hợp với các nhà cung cấp dịch vụ SMS'}
                    {category === 'database' && 'Tích hợp với các hệ quản trị cơ sở dữ liệu'}
                    {category === 'model' && 'Tích hợp với các nhà cung cấp AI và Machine Learning'}
                    {category === 'social' && 'Tích hợp với các mạng xã hội và nền tảng nhắn tin'}
                    {category === 'other' && 'Các tích hợp khác và dịch vụ bổ sung'}
                  </Typography>
                </div>

                {/* Integration Cards */}
                {integrations.length > 0 ? (
                  <ResponsiveGrid
                    maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
                    maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
                    gap={6}
                  >
                    {integrations.map((integration) => (
                      <ModuleCard
                        key={integration.id}
                        title={integration.title}
                        description={integration.description}
                        icon={integration.icon as 'mail' | 'bank' | 'database' | 'openai-red' | 'mail-plus' | 'facebook' | 'website' | 'calendar' | 'truck' | 'cloud' | 'server' | 'users'}
                        linkTo={integration.linkTo}
                        gradientColor={integration.gradientColor || 'primary'}
                      />
                    ))}
                  </ResponsiveGrid>
                ) : (
                  <Card className="p-8">
                    <div className="text-center">
                      <div className="text-gray-500 text-lg mb-2">
                        {t('common:noIntegrationsInCategory', 'Chưa có tích hợp nào trong danh mục này')}
                      </div>
                      <div className="text-gray-400 text-sm">
                        {t('common:comingSoon', 'Sẽ có thêm tích hợp trong tương lai')}
                      </div>
                    </div>
                  </Card>
                )}
              </div>
            );
          })}

          {/* No results message for search */}
          {searchTerm.trim() && Object.values(filteredIntegrationsByCategory).every(integrations => integrations.length === 0) && (
            <Card className="p-12">
              <div className="text-center">
                <div className="text-gray-500 text-lg mb-2">
                  {t('common:noResultsFound', 'Không tìm thấy kết quả nào')}
                </div>
                <div className="text-gray-400 text-sm">
                  {t('common:tryDifferentSearch', 'Thử tìm kiếm với từ khóa khác')}
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserIntegrationManagementPage;
