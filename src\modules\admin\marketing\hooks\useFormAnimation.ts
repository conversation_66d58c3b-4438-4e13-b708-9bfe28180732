import { useState, useEffect, useRef, useLayoutEffect } from 'react';

interface FormAnimationState {
  isVisible: boolean;
  animationClass: string;
}

/**
 * Hook xử lý hiệu ứng animation cho form
 * @param initialState Trạng thái ban đầu của form (hiển thị hay ẩn)
 * @returns Đối tượng chứa trạng thái và các hàm điều khiển animation
 */
export const useFormAnimation = (initialState = false) => {
  // Sử dụng useRef để theo dõi xem component đã được mount lần đầu chưa
  const isFirstMount = useRef(true);

  const [showForm, setShowForm] = useState(initialState);
  const [animationState, setAnimationState] = useState<FormAnimationState>({
    isVisible: false, // Luôn bắt đầu với isVisible = false để tránh hiển thị ban đầu
    animationClass: 'translate-x-full opacity-0',
  });

  // Sử dụng useLayoutEffect để đảm bảo form không hiển thị ngay từ đầu
  useLayoutEffect(() => {
    // Không làm gì cả, chỉ đảm bảo animationState được áp dụng trước khi render
  }, []);

  // Xử lý khi trạng thái showForm thay đổi
  useEffect(() => {
    // Nếu là lần đầu mount component, đánh dấu đã mount và không thực hiện animation
    if (isFirstMount.current) {
      isFirstMount.current = false;
      return;
    }

    if (showForm) {
      // Hiển thị form với animation
      setAnimationState({
        isVisible: true,
        animationClass: 'translate-x-full opacity-0',
      });

      // Đợi một chút để DOM cập nhật, sau đó thêm animation
      setTimeout(() => {
        setAnimationState({
          isVisible: true,
          animationClass: 'translate-x-0 opacity-100',
        });
      }, 10);
    } else {
      // Ẩn form với animation
      setAnimationState({
        isVisible: true,
        animationClass: 'translate-x-full opacity-0',
      });

      // Đợi animation hoàn thành, sau đó ẩn form
      setTimeout(() => {
        setAnimationState({
          isVisible: false,
          animationClass: 'translate-x-full opacity-0',
        });
      }, 300); // Thời gian bằng với thời gian transition
    }
  }, [showForm]);

  // Hàm toggle form
  const toggleForm = () => {
    setShowForm(!showForm);
  };

  return {
    showForm,
    setShowForm,
    toggleForm,
    isFormVisible: animationState.isVisible,
    animationClass: animationState.animationClass,
  };
};

export default useFormAnimation;
