/**
 * Trang thông tin đơn hàng sau khi chọn gói R-Point
 */
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Form, Loading, ResponsiveGrid } from '@/shared/components/common';
import UserInfo from '../components/UserInfo';
import OrderSummary from '../components/OrderSummary';
import PaymentMethod from '../components/PaymentMethod';
import CouponInput from '../components/CouponInput';
import InvoiceInfo from '../components/InvoiceInfo';
import { useRPointOrder } from '../hooks/useRPointOrder';
import { orderFormSchema } from '../schemas/order.schema';

/**
 * Trang thông tin đơn hàng sau khi chọn gói R-Point
 */
const RPointOrderPage: React.FC = () => {
  const { t } = useTranslation(['rpoint']);
  const navigate = useNavigate();
  const location = useLocation();

  // Lấy thông tin gói R-Point từ state
  const packageInfo = location.state?.packageInfo;

  // Nếu không có thông tin gói, chuyển hướng về trang danh sách gói
  useEffect(() => {
    if (!packageInfo) {
      navigate('/rpoint/packages');
    }
  }, [packageInfo, navigate]);

  // Sử dụng hook quản lý state và logic - luôn gọi hook trước điều kiện return
  const {
    defaultValues,
    couponCode,
    discount,
    suggestedCoupons,
    randomCoupons,
    isApplyingCoupon,
    isCreatingOrder,
    isLoadingCoupons,
    handleApplyCoupon,
    handleSubmit,
  } = useRPointOrder({
    packageId: packageInfo?.id,
    points: packageInfo?.points,
    price: packageInfo?.price,
    packageName: packageInfo?.name,
  });

  // Nếu không có thông tin gói, hiển thị loading
  if (!packageInfo) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading />
      </div>
    );
  }

  return (
    <div>
      <Form
        schema={orderFormSchema}
        // @ts-expect-error - Kiểu dữ liệu không khớp nhưng vẫn hoạt động
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        <ResponsiveGrid maxColumns={{ xs: 1, md: 3 }} gap={6} className="mb-6">
          {/* Cột bên trái: Thông tin người dùng và thông tin xuất hóa đơn */}
          <div className="md:col-span-2 space-y-6">
            {/* Thông tin người dùng */}
            <UserInfo />

            {/* Thông tin xuất hóa đơn */}
            <InvoiceInfo />

            {/* Phương thức thanh toán */}
            <PaymentMethod />

            {/* Mã khuyến mãi */}
            <CouponInput
              onApplyCoupon={handleApplyCoupon}
              suggestedCoupons={suggestedCoupons}
              randomCoupons={randomCoupons}
              {...(couponCode && { appliedCoupon: couponCode })}
              isLoading={isApplyingCoupon}
              isLoadingCoupons={isLoadingCoupons}
            />
          </div>

          {/* Cột bên phải: Chi tiết đơn hàng */}
          <div className="space-y-6">
            {/* Chi tiết đơn hàng */}
            <OrderSummary
              points={packageInfo.points}
              price={packageInfo.price}
              discount={discount}
              vatRate={0} // 0% VAT cho loại hàng không mất phí
            />

            {/* Nút thanh toán */}
            <Button variant="primary" size="lg" fullWidth type="submit" isLoading={isCreatingOrder}>
              {t('rpoint.order.checkout', 'Thanh toán')}
            </Button>

            {/* Nút quay lại */}
            <Button
              variant="ghost"
              fullWidth
              onClick={() => navigate('/rpoint/packages')}
              disabled={isCreatingOrder}
            >
              {t('rpoint.order.back', 'Quay lại')}
            </Button>
          </div>
        </ResponsiveGrid>
      </Form>
    </div>
  );
};

export default RPointOrderPage;
