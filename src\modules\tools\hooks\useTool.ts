import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ToolService } from '../services/user-tool.service';
import {
  ToolQueryParams,
  CloneAdminToolParams,
  UpdateFromAdminParams,
  CloneAllPublicToolsParams,
  RollbackToAdminVersionParams,
} from '../types/tool.types';
import { EditUserToolVersionParams } from '../types/user-tool.types';

// Khởi tạo service
const toolService = new ToolService();

// Các key cho React Query
export const TOOL_QUERY_KEYS = {
  all: ['tools'] as const,
  lists: () => [...TOOL_QUERY_KEYS.all, 'list'] as const,
  list: (params: ToolQueryParams) => [...TOOL_QUERY_KEYS.lists(), params] as const,
  details: () => [...TOOL_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...TOOL_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách tool của người dùng
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useUserTools = (params?: ToolQueryParams) => {
  return useQuery({
    queryKey: TOOL_QUERY_KEYS.list(params || {}),
    queryFn: () => toolService.getUserTools(params),
  });
};

/**
 * Hook để lấy thông tin chi tiết tool của người dùng
 * @param id ID của tool
 * @returns Query object
 */
export const useUserToolDetail = (id?: string) => {
  return useQuery({
    queryKey: TOOL_QUERY_KEYS.detail(id || ''),
    queryFn: () => toolService.getUserToolById(id || ''),
    enabled: !!id,
  });
};

/**
 * Hook để sao chép tool từ admin
 * @returns Mutation object
 */
export const useCloneAdminTool = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: CloneAdminToolParams) => toolService.cloneAdminTool(params),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TOOL_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để cập nhật tool từ phiên bản mới của admin
 * @returns Mutation object
 */
export const useUpdateFromAdmin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: UpdateFromAdminParams) => toolService.updateFromAdmin(params),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: TOOL_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({
        queryKey: TOOL_QUERY_KEYS.detail(variables.userToolId),
      });
    },
  });
};

/**
 * Hook để sao chép tất cả tool công khai từ admin
 * @returns Mutation object
 */
export const useCloneAllPublicTools = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: CloneAllPublicToolsParams) => toolService.cloneAllPublicTools(params),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TOOL_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để chỉnh sửa phiên bản tool
 * @returns Mutation object
 */
// export const useEditToolVersion = () => {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: ({
//       toolId,
//       versionId,
//       params
//     }: {
//       toolId: string;
//       versionId: string;
//       params: EditUserToolVersionParams
//     }) => toolService.editToolVersion(toolId, versionId, params),
//     onSuccess: (_, variables) => {
//       queryClient.invalidateQueries({
//         queryKey: TOOL_QUERY_KEYS.detail(variables.toolId)
//       });
//     },
//   });
// };

/**
 * Hook để khôi phục về phiên bản gốc từ admin
 * @returns Mutation object
 */
export const useRollbackToAdminVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: RollbackToAdminVersionParams) =>
      toolService.rollbackToAdminVersion(params),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: TOOL_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({
        queryKey: TOOL_QUERY_KEYS.detail(variables.userToolId),
      });
    },
  });
};

/**
 * Hook để xóa tool
 * @returns Mutation object
 */
export const useDeleteTool = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => toolService.deleteTool(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TOOL_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để kích hoạt/vô hiệu hóa tool
 * @returns Mutation object
 */
export const useToggleToolActive = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => toolService.toggleToolActive(id),
    onSuccess: (_, toolId) => {
      queryClient.invalidateQueries({ queryKey: TOOL_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: TOOL_QUERY_KEYS.detail(toolId) });
    },
  });
};

/**
 * Hook để chỉnh sửa phiên bản tool
 * @returns Mutation object
 */
export const useEditToolVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      toolId,
      versionId,
      params,
    }: {
      toolId: string;
      versionId: string;
      params: EditUserToolVersionParams;
    }) => toolService.editToolVersion(toolId, versionId, params),
    onSuccess: (_, { toolId }) => {
      queryClient.invalidateQueries({ queryKey: TOOL_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: TOOL_QUERY_KEYS.detail(toolId) });
    },
  });
};
