import { apiClient } from '@/shared/api/axios';

/**
 * Interface cho tham số phân trang
 */
export interface PaginationParams {
  page: number;
  limit: number;
}

/**
 * Interface cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  items: T[];
  meta?: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Interface cho Knowledge File response
 */
export interface UserKnowledgeFileDto {
  id: string;
  name: string;
  extension?: string;
  description?: string;
  mime?: string;
  storage?: number;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface cho User Agent response
 */
export interface UserAgentDto {
  id: number;
  name: string;
  description?: string;
  avatar?: string;
  category?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface cho API response với pagination
 */
export interface ApiPaginatedResponse<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Interface cho source option
 */
export interface SourceOption {
  value: string;
  label: string;
}

/**
 * Load knowledge files từ API /v1/user/knowledge-files
 * @param inputValue Từ khóa tìm kiếm
 * @param pagination Thông tin phân trang
 * @returns Promise với danh sách SourceOption hoặc PaginatedResult
 */
export const loadUserKnowledgeFiles = async (
  inputValue: string,
  pagination?: PaginationParams
): Promise<SourceOption[] | PaginatedResult<SourceOption>> => {
  try {
    const params: Record<string, string | number> = {};

    if (inputValue.trim()) {
      params['search'] = inputValue.trim();
    }

    if (pagination) {
      params['page'] = pagination.page;
      params['limit'] = pagination.limit;
    }

    const response = await apiClient.get<ApiPaginatedResponse<UserKnowledgeFileDto>>(
      '/user/knowledge-files',
      {
        params
      }
    );

    const data = response.result;

    // Transform data to SourceOption format
    const items: SourceOption[] = data.items.map((file) => ({
      value: file.id,
      label: file.name,
    }));

    // Return paginated result if pagination info exists
    if (data.meta) {
      return {
        items,
        meta: data.meta
      };
    }

    return items;
  } catch (error) {
    console.error('Error loading user knowledge files:', error);
    return [];
  }
};

/**
 * Load user agents từ API /v1/user/agents
 * @param inputValue Từ khóa tìm kiếm theo name
 * @param pagination Thông tin phân trang
 * @returns Promise với danh sách SourceOption hoặc PaginatedResult
 */
export const loadUserAgents = async (
  inputValue: string,
  pagination?: PaginationParams
): Promise<SourceOption[] | PaginatedResult<SourceOption>> => {
  try {
    const params: Record<string, string | number> = {};

    if (inputValue.trim()) {
      params['search'] = inputValue.trim(); // Tìm kiếm theo name
    }

    if (pagination) {
      params['page'] = pagination.page;
      params['limit'] = pagination.limit;
    }

    const response = await apiClient.get<ApiPaginatedResponse<UserAgentDto>>(
      '/user/agents',
      {
        params
      }
    );

    const data = response.result;

    // Transform data to SourceOption format
    const items: SourceOption[] = data.items.map((agent) => ({
      value: String(agent.id),
      label: agent.name,
    }));

    // Return paginated result if pagination info exists
    if (data.meta) {
      return {
        items,
        meta: data.meta
      };
    }

    return items;
  } catch (error) {
    console.error('Error loading user agents:', error);
    return [];
  }
};
