import React, { useState, useCallback } from 'react';
import { Typography, Pagination } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useUserDataFineTuneList } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import { DatasetGrid } from '../components';
import {
  DataFineTuneStatus,
  UserDataFineTuneQueryDto,
  UserDataFineTuneSortBy,
  UserDataFineTuneResponseDto,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import ProviderSelectionCards from '../components/ProviderSelectionModal';

// Interface cho backend response structure
interface BackendDatasetResponse {
  items: UserDataFineTuneResponseDto[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Data Fine-tune Page - Hiển thị danh sách dataset fine-tune
 */
const DataFineTunePage: React.FC = () => {
  const { t } = useTranslation('admin-dataset');
  const navigate = useNavigate();

  const [queryParams, setQueryParams] = useState<UserDataFineTuneQueryDto>({
    page: 1,
    limit: 12,
    search: '',
    sortBy: UserDataFineTuneSortBy.CREATED_AT,
  });

  const [showProviderCards, setShowProviderCards] = useState(false);

  const handleCreateNew = () => {
    // Toggle hiển thị cards
    setShowProviderCards(prev => !prev);
  };

  const { data, isLoading } = useUserDataFineTuneList(queryParams);

  // Type assertion để match với backend response
  const backendData = data as unknown as BackendDatasetResponse;

  // Handle search
  const handleSearch = (value: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({
      ...prev,
      page,
    }));
  };

  const handleItemsPerPageChange = useCallback((newItemsPerPage: number) => {
    setQueryParams(prev => ({
      ...prev,
      limit: newItemsPerPage,
      page: 1, // Reset về trang 1
    }));
  }, []);

  const handleSelectDataset = (id: string) => {
    // Navigate to dataset detail page (sẽ tạo sau)
    console.log('Navigate to dataset detail:', id);
  };

  // Handle filter status
  const handleFilterStatus = useCallback((status: string) => {
    setQueryParams(prev => {
      const newParams = { ...prev, page: 1 };
      if (status === 'all') {
        delete newParams.status;
      } else {
        newParams.status = status as DataFineTuneStatus;
      }
      return newParams;
    });
  }, []);

  return (
    <div className="space-y-6">
      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleCreateNew}
        items={[
          {
            id: 'all',
            label: t('dataFineTunePage.filter.allStatus'),
            icon: 'list',
            onClick: () => handleFilterStatus('all'),
          },
          {
            id: 'pending',
            label: t('dataFineTunePage.status.pending'),
            icon: 'clock',
            onClick: () => handleFilterStatus(DataFineTuneStatus.PENDING),
          },
          {
            id: 'processing',
            label: t('dataFineTunePage.status.processing'),
            icon: 'refresh',
            onClick: () => handleFilterStatus(DataFineTuneStatus.PROCESSING),
          },
          {
            id: 'completed',
            label: t('dataFineTunePage.status.completed'),
            icon: 'check-circle',
            onClick: () => handleFilterStatus(DataFineTuneStatus.COMPLETED),
          },
          {
            id: 'failed',
            label: t('dataFineTunePage.status.failed'),
            icon: 'x-circle',
            onClick: () => handleFilterStatus(DataFineTuneStatus.FAILED),
          },
          {
            id: 'cancelled',
            label: t('dataFineTunePage.status.cancelled'),
            icon: 'ban',
            onClick: () => handleFilterStatus(DataFineTuneStatus.CANCELLED),
          },
        ]}
        showDateFilter={false}
        showColumnFilter={false}
        isLoading={isLoading}
      />

      {showProviderCards && (
        <ProviderSelectionCards
          onSelectOpenAI={() => {
            setShowProviderCards(false);
            navigate('/admin/dataset/create-openai');
          }}
          onSelectGoogle={() => {
            setShowProviderCards(false);
            navigate('/admin/dataset/create-google');
          }}
        />
      )}

      {/* Dataset Grid */}
      {backendData?.items && backendData.items.length > 0 ? (
        <>
          <DatasetGrid
            datasets={backendData.items}
            onViewDataset={dataset => handleSelectDataset(dataset.id)}
            onEditDataset={dataset => {
              // Navigate to edit dataset page (sẽ tạo sau)
              console.log('Navigate to edit dataset:', dataset.id);
            }}
            onDeleteDataset={dataset => {
              // Handle delete dataset (sẽ tạo sau)
              console.log('Delete dataset:', dataset.id);
            }}
          />

          {/* Pagination - Hiển thị khi có dữ liệu */}
          {backendData.meta && backendData.meta.totalItems > 0 && (
            <div className="flex justify-end mt-8">
              <Pagination
                variant="simple"
                currentPage={queryParams.page ?? 1}
                itemsPerPage={queryParams.limit ?? 10}
                totalPages={backendData.meta?.totalPages ?? 1}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                showFirstLastButtons={false}
                showItemsPerPageSelector={true}
                showPageInfo={false}
                itemsPerPageOptions={[10, 20, 50, 100]}
                size="md"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <Typography variant="body1" color="muted" className="text-lg">
            {t('dataFineTunePage.messages.noDatasets')}
          </Typography>
        </div>
      )}
    </div>
  );
};

export default DataFineTunePage;
