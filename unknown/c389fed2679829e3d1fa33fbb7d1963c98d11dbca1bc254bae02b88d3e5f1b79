/**
 * Hooks for tag API using TanStack Query
 */

import { useMutation, useQuery, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { TagService } from '../services/tag.service';
import { CreateTagRequest, UpdateTagRequest, TagQueryParams, Tag } from '../types/tag.types';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Query keys for tag API
 */
export const TAG_QUERY_KEYS = {
  all: ['marketing', 'tags'] as const,
  list: (params?: TagQueryParams) => [...TAG_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...TAG_QUERY_KEYS.all, 'detail', id] as const,
};

/**
 * Hook to get all tags with optional filtering
 */
export const useTags = (params?: TagQueryParams) => {
  return useQuery({
    queryKey: TAG_QUERY_KEYS.list(params),
    queryFn: () => TagService.getTags(params),
    select: data => data.result,
  });
};

/**
 * Hook to get tag by ID
 */
export const useTag = (
  id: number,
  options?: Omit<UseQueryOptions<ApiResponseDto<Tag>, Error, Tag>, 'queryKey' | 'queryFn' | 'select'>
) => {
  return useQuery({
    queryKey: TAG_QUERY_KEYS.detail(id),
    queryFn: () => TagService.getTagById(id),
    select: data => data.result,
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook to create tag
 */
export const useCreateTag = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTagRequest) => TagService.createTag(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to update tag
 */
export const useUpdateTag = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateTagRequest) => TagService.updateTag(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to update tag with dynamic ID
 */
export const useUpdateTagDynamic = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateTagRequest }) => TagService.updateTag(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to delete tag
 */
export const useDeleteTag = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => TagService.deleteTag(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to delete multiple tags
 */
export const useDeleteMultipleTags = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: number[]) => TagService.deleteMultipleTags(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.all });
    },
  });
};
