import { Button, Icon } from '@/shared/components/common';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import ConvertFieldForm from './ConvertFieldForm';
import ConvertFieldItem, { ConvertField } from './ConvertFieldItem';
import { useGetConversion, useUpdateConversion } from '../../hooks/useConversion';

// Interface cho dữ liệu cấu hình chuyển đổi
export interface ConvertConfigData {
  fields: ConvertField[];
}

interface ConvertConfigProps {
  agentId?: string;
  initialData?: ConvertConfigData;
  onSave?: (data: ConvertConfigData) => void;
}

/**
 * Component cấu hình chuyển đổi dữ liệu cho Agent
 */
const ConvertConfig: React.FC<ConvertConfigProps> = ({
  agentId,
  initialData,
  onSave
}) => {
  const { t } = useTranslation('aiAgents');

  // Fetch conversion data từ API nếu có agentId
  const { data: conversionResponse } = useGetConversion(agentId);
  const updateConversionMutation = useUpdateConversion();

  // State cho dữ liệu cấu hình
  const [configData, setConfigData] = useState<ConvertConfigData>(initialData || {
    fields: [
      {
        id: 'field-1',
        name: 'email',
        description: 'Lấy tất cả email của người dùng',
        enabled: true,
        type: 'email',
        required: true
      },
      {
        id: 'field-2',
        name: 'phones',
        description: 'Lấy số điện thoại của người dùng',
        enabled: true,
        type: 'phone',
        required: true
      },
      {
        id: 'field-3',
        name: 'name',
        description: 'Lấy họ tên đầy đủ của người dùng',
        enabled: true,
        type: 'name',
        required: true
      },
      {
        id: 'field-4',
        name: 'address',
        description: 'Lấy địa chỉ của người dùng',
        enabled: false,
        type: 'address',
        required: false
      },
      {
        id: 'field-5',
        name: 'birthday',
        description: 'Lấy ngày sinh của người dùng',
        enabled: false,
        type: 'date',
        required: false
      }
    ]
  });

  // Update local state khi có data từ API
  useEffect(() => {
    if (conversionResponse?.result) {
      const apiData = conversionResponse.result;
      // Convert API data to local format
      const convertedFields = apiData.fields.map((field, index) => ({
        id: `field-${index + 1}`,
        name: field.name,
        description: field.description || '',
        enabled: true, // Assume enabled if in API
        type: field.type as ConvertField['type'],
        required: Boolean(field.required)
      }));

      setConfigData({
        fields: convertedFields
      });
    }
  }, [conversionResponse]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingField, setEditingField] = useState<ConvertField | null>(null);

  // Helper function để save data
  const saveConversionData = (newData: ConvertConfigData) => {
    if (agentId) {
      // Convert local format to API format
      const apiData = {
        fields: newData.fields.map(field => ({
          name: field.name,
          type: field.type as 'string' | 'number' | 'boolean' | 'array' | 'object',
          description: field.description,
          required: Boolean(field.required)
        }))
      };

      updateConversionMutation.mutate({
        agentId,
        data: apiData
      });
    }

    // Gọi callback onSave nếu có
    if (onSave) {
      onSave(newData);
    }
  };

  // Xử lý khi thay đổi trạng thái bật/tắt của trường
  const handleToggleField = (id: string) => {
    const updatedFields = configData.fields.map(field => {
      if (field.id === id) {
        return { ...field, enabled: !field.enabled };
      }
      return field;
    });

    const newData = {
      ...configData,
      fields: updatedFields
    };

    setConfigData(newData);
    saveConversionData(newData);
  };

  // Xử lý khi thêm trường mới
  const handleAddField = (field: ConvertField) => {
    const newData = {
      ...configData,
      fields: [...configData.fields, field]
    };

    setConfigData(newData);
    setShowAddForm(false);
    saveConversionData(newData);
  };

  // Xử lý khi chỉnh sửa trường
  const handleEditField = (field: ConvertField) => {
    const updatedFields = configData.fields.map(f => {
      if (f.id === field.id) {
        return field;
      }
      return f;
    });

    const newData = {
      ...configData,
      fields: updatedFields
    };

    setConfigData(newData);
    setEditingField(null);
    saveConversionData(newData);
  };

  // Xử lý khi xóa trường
  const handleDeleteField = (id: string) => {
    const confirmed = window.confirm('Bạn có chắc chắn muốn xóa trường này?');
    if (!confirmed) return;

    const updatedFields = configData.fields.filter(field => field.id !== id);

    const newData = {
      ...configData,
      fields: updatedFields
    };

    setConfigData(newData);
    saveConversionData(newData);
  };

  return (
    <ConfigComponentWrapper
      componentId="convert"
      title={
        <div className="flex items-center">
          <Icon name="database" size="md" className="mr-2" />
          <span>Cấu hình chuyển đổi</span>
        </div>
      }
    >
      <div className="p-4 space-y-4 sm:space-y-6">
        {/* Tiêu đề chính */}
        <div className="mb-4 sm:mb-6 text-center">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {t('convertConfig.configureFields')}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t('convertConfig.configureFieldsDescription')}
          </p>
        </div>

        {/* Danh sách các trường */}
        <div className="space-y-3">
          {configData.fields.length > 0 ? (
            configData.fields.map(field => (
              <ConvertFieldItem
                key={field.id}
                field={field}
                onToggle={handleToggleField}
                onEdit={() => setEditingField(field)}
                onDelete={handleDeleteField}
              />
            ))
          ) : (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
              {t('convertConfig.noFields')}
            </div>
          )}
        </div>

        {/* Nút thêm trường mới */}
        {!showAddForm && !editingField && (
          <div className="flex justify-center">
            <Button
              variant="outline"
              onClick={() => setShowAddForm(true)}
              className="w-full sm:w-auto"
            >
              <Icon name="plus" size="sm" className="mr-1" />
              {t('convertConfig.addField')}
            </Button>
          </div>
        )}

        {/* Form thêm trường mới */}
        {showAddForm && (
          <ConvertFieldForm
            onSave={handleAddField}
            onCancel={() => setShowAddForm(false)}
          />
        )}

        {/* Form chỉnh sửa trường */}
        {editingField && (
          <ConvertFieldForm
            field={editingField}
            onSave={handleEditField}
            onCancel={() => setEditingField(null)}
          />
        )}
      </div>
    </ConfigComponentWrapper>
  );
};

export default ConvertConfig;
