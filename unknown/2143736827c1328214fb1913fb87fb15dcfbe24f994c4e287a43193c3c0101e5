import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Typography,
  Textarea,
} from '@/shared/components/common';
import { z } from 'zod';
import { TemplateEmailStatus, TemplateEmailType } from '../../types/template-email.types';

// Schema cho form
const formSchema = z.object({
  name: z.string().min(1, 'Tên mẫu email là bắt buộc'),
  description: z.string().optional(),
  subject: z.string().min(1, 'Tiêu đề email là bắt buộc'),
  content: z.string().min(1, 'Nội dung email là bắt buộc'),
  type: z.nativeEnum(TemplateEmailType, {
    errorMap: () => ({ message: 'Loại mẫu email là bắt buộc' }),
  }),
  status: z.nativeEnum(TemplateEmailStatus, {
    errorMap: () => ({ message: 'Trạng thái là bắt buộc' }),
  }),
});

export type TemplateEmailFormValues = z.infer<typeof formSchema>;

interface TemplateEmailFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
}

/**
 * Component form thêm/sửa mẫu email
 */
const TemplateEmailForm: React.FC<TemplateEmailFormProps> = ({ onSubmit, onCancel }) => {
  const { t } = useTranslation('marketing');

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        {t('templateEmail.addNew', 'Thêm mẫu email mới')}
      </Typography>

      <Form schema={formSchema} onSubmit={onSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="name" label="Tên mẫu" required>
            <Input placeholder="Nhập tên mẫu email" fullWidth />
          </FormItem>

          <FormItem name="type" label="Loại mẫu" required>
            <Select
              options={[
                { value: TemplateEmailType.WELCOME, label: 'Chào mừng' },
                { value: TemplateEmailType.NEWSLETTER, label: 'Bản tin' },
                { value: TemplateEmailType.PROMOTION, label: 'Khuyến mãi' },
                { value: TemplateEmailType.NOTIFICATION, label: 'Thông báo' },
                { value: TemplateEmailType.TRANSACTIONAL, label: 'Giao dịch' },
                { value: TemplateEmailType.CUSTOM, label: 'Tùy chỉnh' },
              ]}
              placeholder="Chọn loại mẫu"
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem name="description" label="Mô tả">
          <Textarea placeholder="Nhập mô tả" rows={2} fullWidth />
        </FormItem>

        <FormItem name="subject" label="Tiêu đề email" required>
          <Input placeholder="Nhập tiêu đề email" fullWidth />
        </FormItem>

        <FormItem name="content" label="Nội dung email" required>
          <Textarea placeholder="Nhập nội dung email" rows={10} fullWidth />
        </FormItem>

        <FormItem name="status" label="Trạng thái" required>
          <Select
            options={[
              { value: TemplateEmailStatus.DRAFT, label: 'Bản nháp' },
              { value: TemplateEmailStatus.ACTIVE, label: 'Hoạt động' },
              { value: TemplateEmailStatus.INACTIVE, label: 'Không hoạt động' },
            ]}
            placeholder="Chọn trạng thái"
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onCancel}>
            Hủy
          </Button>
          <Button type="submit" variant="primary">
            Lưu
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default TemplateEmailForm;
