/**
 * Types for campaign API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Campaign status enum
 */
export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

/**
 * Campaign type enum
 */
export enum CampaignType {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  SOCIAL = 'social',
  MULTI_CHANNEL = 'multi_channel',
}

/**
 * Campaign metrics
 */
export interface CampaignMetrics {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  converted: number;
  bounced: number;
  unsubscribed: number;
}

/**
 * Campaign history item
 */
export interface CampaignHistoryItem {
  id: number;
  campaignId: number;
  action: string;
  description: string;
  performedBy: string;
  performedAt: string;
}

/**
 * Campaign entity
 */
export interface Campaign {
  id: number;
  name: string;
  description?: string;
  type: CampaignType;
  status: CampaignStatus;
  segmentId: number;
  segmentName: string;
  audienceId: number;
  audienceName: string;
  totalContacts: number;
  metrics: CampaignMetrics;
  startDate: string;
  endDate: string | null;
  createdAt: string;
  updatedAt: string;
}

/**
 * Create campaign request
 */
export interface CreateCampaignRequest {
  name: string;
  description?: string;
  type: CampaignType;
  segmentId: number;
  startDate?: string;
  endDate?: string;
  status?: CampaignStatus;
  content?: string;
  templateId?: number;
}

/**
 * Update campaign request
 */
export interface UpdateCampaignRequest {
  name?: string;
  description?: string;
  type?: CampaignType;
  segmentId?: number;
  startDate?: string;
  endDate?: string;
  status?: CampaignStatus;
  content?: string;
  templateId?: number;
}

/**
 * Campaign response
 */
export type CampaignResponse = Campaign;

/**
 * Campaign history response
 */
export type CampaignHistoryResponse = CampaignHistoryItem;

/**
 * Campaign list response
 */
export type CampaignListResponse = ApiResponseDto<PaginatedResult<CampaignResponse>>;

/**
 * Campaign detail response
 */
export type CampaignDetailResponse = ApiResponseDto<CampaignResponse>;

/**
 * Campaign history list response
 */
export type CampaignHistoryListResponse = ApiResponseDto<CampaignHistoryResponse[]>;

/**
 * Campaign query params
 */
export interface CampaignQueryParams {
  search?: string;
  status?: CampaignStatus;
  type?: CampaignType;
  segmentId?: number;
  audienceId?: number;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: string;
}
