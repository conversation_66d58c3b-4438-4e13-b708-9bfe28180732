/**
 * Types for marketing statistics API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Marketing statistics query params
 */
export interface MarketingStatisticsQueryParams {
  startDate?: string;
  endDate?: string;
  period?: 'day' | 'week' | 'month' | 'year';
  sortBy?: string | undefined;
  sortDirection?: string | undefined;
  page?: number;
  limit?: number;
}

/**
 * Marketing overview statistics
 */
export interface MarketingOverviewStatistics {
  totalAudiences: number;
  totalSegments: number;
  totalCampaigns: number;
  activeCampaigns: number;
  totalContacts: number;
  tagsCount: number;
  contactsGrowth: number;
  customFieldsCount: number;
  campaignPerformance: {
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    converted: number;
  };
  recentCampaigns: {
    id: number;
    name: string;
    type: string;
    status: string;
    sentCount: number;
    openRate: number;
    clickRate: number;
    date: string;
  }[];
}

/**
 * Audience growth statistics
 */
export interface AudienceGrowthStatistics {
  totalContacts: number;
  growth: number;
  growthPercentage: number;
  byPeriod: {
    period: string;
    count: number;
    growth: number;
  }[];
  bySource: {
    source: string;
    count: number;
    percentage: number;
  }[];
  byType: {
    type: string;
    count: number;
    percentage: number;
  }[];
}

/**
 * Campaign performance statistics
 */
export interface CampaignPerformanceStatistics {
  totalCampaigns: number;
  activeCampaigns: number;
  totalSent: number;
  averageOpenRate: number;
  averageClickRate: number;
  averageConversionRate: number;
  byType: {
    type: string;
    count: number;
    openRate: number;
    clickRate: number;
    conversionRate: number;
  }[];
  byPeriod: {
    period: string;
    sent: number;
    opened: number;
    clicked: number;
    converted: number;
  }[];
}

/**
 * Segment distribution statistics
 */
export interface SegmentDistributionStatistics {
  totalSegments: number;
  totalContacts: number;
  averageContactsPerSegment: number;
  bySize: {
    size: string;
    count: number;
    percentage: number;
  }[];
  byType: {
    type: string;
    count: number;
    percentage: number;
  }[];
  topSegments: {
    id: number;
    name: string;
    contactCount: number;
    percentage: number;
  }[];
}

/**
 * Marketing overview statistics response
 */
export type MarketingOverviewStatisticsResponse = ApiResponseDto<MarketingOverviewStatistics>;

/**
 * Audience growth statistics response
 */
export type AudienceGrowthStatisticsResponse = ApiResponseDto<AudienceGrowthStatistics>;

/**
 * Campaign performance statistics response
 */
export type CampaignPerformanceStatisticsResponse = ApiResponseDto<CampaignPerformanceStatistics>;

/**
 * Segment distribution statistics response
 */
export type SegmentDistributionStatisticsResponse = ApiResponseDto<SegmentDistributionStatistics>;

/**
 * Marketing overview response DTO (từ backend API)
 */
export interface MarketingOverviewResponseDto {
  /**
   * Tổng số templates
   */
  totalTemplates: number;

  /**
   * Tỷ lệ mở email (%)
   */
  openRate: number;

  /**
   * Tỷ lệ click email (%)
   */
  clickRate: number;

  /**
   * Tổng số email đã gửi
   */
  totalEmailsSent: number;
}

/**
 * Recent template DTO
 */
export interface RecentTemplateDto {
  id: string;
  name: string;
  subject: string;
  status: 'ACTIVE' | 'DRAFT';
  createdAt: string;
  updatedAt: string;
}

/**
 * Recent templates response DTO
 */
export interface RecentTemplatesResponseDto {
  /**
   * Danh sách 5 templates gần nhất
   */
  templates: RecentTemplateDto[];
}

/**
 * Marketing overview response
 */
export type MarketingOverviewResponse = ApiResponseDto<MarketingOverviewResponseDto>;

/**
 * Recent templates response
 */
export type RecentTemplatesResponse = ApiResponseDto<RecentTemplatesResponseDto>;

/**
 * Recent campaign DTO (từ backend API)
 */
export interface RecentCampaignDto {
  /**
   * ID của chiến dịch
   */
  id: number;

  /**
   * Tên chiến dịch
   */
  name: string;

  /**
   * Tổng số người nhận
   */
  totalRecipients: number;

  /**
   * Trạng thái chiến dịch
   */
  status: 'DRAFT' | 'SCHEDULED' | 'SENDING' | 'SENT' | 'FAILED' | 'CANCELLED';

  /**
   * Thời gian chạy chiến dịch (Unix timestamp)
   */
  runAt: number;

  /**
   * Tỷ lệ tin nhắn đã gửi (%)
   */
  sentRate?: number;

  /**
   * Tỷ lệ tin nhắn click (%)
   */
  clickRate?: number;
}

/**
 * Recent campaigns response DTO (từ backend API)
 */
export interface RecentCampaignsResponseDto {
  /**
   * Danh sách chiến dịch gần đây
   */
  campaigns: RecentCampaignDto[];

  /**
   * Tổng số chiến dịch
   */
  totalCampaigns: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  updatedAt: number;
}

/**
 * Recent campaigns response
 */
export type RecentCampaignsResponse = ApiResponseDto<RecentCampaignsResponseDto>;
