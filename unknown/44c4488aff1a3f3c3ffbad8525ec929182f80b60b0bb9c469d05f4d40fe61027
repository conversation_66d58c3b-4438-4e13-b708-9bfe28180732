/**
 * Service for campaign API
 */

import { apiClient } from '@/shared/api';
import {
  CampaignDetailResponse,
  CampaignHistoryListResponse,
  CampaignListResponse,
  CampaignQueryParams,
  CreateCampaignRequest,
  UpdateCampaignRequest,
} from '../types/campaign.types';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Base URL for campaign API
 */
const BASE_URL = '/marketing/campaigns';

/**
 * Campaign service
 */
export const CampaignService = {
  /**
   * Get campaigns
   */
  getCampaigns: async (params?: CampaignQueryParams): Promise<CampaignListResponse> => {
    return apiClient.get<CampaignListResponse['result']>(BASE_URL, { params });
  },

  /**
   * Get campaign by ID
   */
  getCampaignById: async (id: number): Promise<CampaignDetailResponse> => {
    return apiClient.get<CampaignDetailResponse['result']>(`${BASE_URL}/${id}`);
  },

  /**
   * Get campaign history
   */
  getCampaignHistory: async (id: number): Promise<CampaignHistoryListResponse> => {
    return apiClient.get<CampaignHistoryListResponse['result']>(`${BASE_URL}/${id}/history`);
  },

  /**
   * Create campaign
   */
  createCampaign: async (data: CreateCampaignRequest): Promise<CampaignDetailResponse> => {
    return apiClient.post<CampaignDetailResponse['result']>(BASE_URL, data);
  },

  /**
   * Update campaign
   */
  updateCampaign: async (
    id: number,
    data: UpdateCampaignRequest
  ): Promise<CampaignDetailResponse> => {
    return apiClient.put<CampaignDetailResponse['result']>(`${BASE_URL}/${id}`, data);
  },

  /**
   * Delete campaign
   */
  deleteCampaign: async (id: number): Promise<ApiResponseDto<{ success: boolean }>> => {
    return apiClient.delete<{ success: boolean }>(`${BASE_URL}/${id}`);
  },

  /**
   * Run campaign
   */
  runCampaign: async (id: number): Promise<CampaignDetailResponse> => {
    return apiClient.post<CampaignDetailResponse['result']>(`${BASE_URL}/${id}/run`);
  },
};
