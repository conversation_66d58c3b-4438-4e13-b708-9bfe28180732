/**
 * Hooks for marketing statistics API using TanStack Query
 */

import { useQuery } from '@tanstack/react-query';
import { MarketingStatisticsService } from '../services/statistics.service';
import { MarketingStatisticsQueryParams } from '../types/statistics.types';

/**
 * Query keys for marketing statistics API
 */
export const STATISTICS_QUERY_KEYS = {
  all: ['marketing', 'statistics'] as const,
  overview: (params?: MarketingStatisticsQueryParams) =>
    [...STATISTICS_QUERY_KEYS.all, 'overview', params] as const,
  audienceGrowth: (params?: MarketingStatisticsQueryParams) =>
    [...STATISTICS_QUERY_KEYS.all, 'audience-growth', params] as const,
  campaignPerformance: (params?: MarketingStatisticsQueryParams) =>
    [...STATISTICS_QUERY_KEYS.all, 'campaign-performance', params] as const,
  segmentDistribution: () => [...STATISTICS_QUERY_KEYS.all, 'segment-distribution'] as const,
};

/**
 * Thống kê tổng quan marketing
 */
export interface MarketingOverviewStatistics {
  totalContacts: number;
  totalSegments: number;
  activeCampaigns: number;
  totalCampaigns: number;
  customFieldsCount: number;
  tagsCount: number;
  // Thêm các trường mới
  emailTemplatesCount: number;
  smsCampaignsCount: number;
  googleAdsAccounts: number;
  facebookAdsAccounts: number;
  zaloOaAccounts: number;
}

/**
 * Hook lấy thống kê tổng quan marketing
 */
export const useOverviewStatistics = () => {
  return useQuery<MarketingOverviewStatistics>({
    queryKey: ['marketingOverviewStatistics'],
    queryFn: async () => {
      // TODO: Thay thế bằng API call thực tế
      // const response = await fetch('/api/marketing/statistics/overview');
      // return response.json();

      // Mock data
      return {
        totalContacts: 1250,
        totalSegments: 15,
        activeCampaigns: 3,
        totalCampaigns: 8,
        customFieldsCount: 12,
        tagsCount: 24,
        // Dữ liệu mới
        emailTemplatesCount: 7,
        smsCampaignsCount: 5,
        googleAdsAccounts: 2,
        facebookAdsAccounts: 1,
        zaloOaAccounts: 1,
      };
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook to get audience growth statistics
 */
export const useAudienceGrowthStatistics = (params?: MarketingStatisticsQueryParams) => {
  return useQuery({
    queryKey: STATISTICS_QUERY_KEYS.audienceGrowth(params),
    queryFn: () => MarketingStatisticsService.getAudienceGrowthStatistics(params),
    select: data => data.result,
  });
};

/**
 * Hook to get campaign performance statistics
 */
export const useCampaignPerformanceStatistics = (params?: MarketingStatisticsQueryParams) => {
  return useQuery({
    queryKey: STATISTICS_QUERY_KEYS.campaignPerformance(params),
    queryFn: () => MarketingStatisticsService.getCampaignPerformanceStatistics(params),
    select: data => data.result,
  });
};

/**
 * Hook to get segment distribution statistics
 */
export const useSegmentDistributionStatistics = () => {
  return useQuery({
    queryKey: STATISTICS_QUERY_KEYS.segmentDistribution(),
    queryFn: () => MarketingStatisticsService.getSegmentDistributionStatistics(),
    select: data => data.result,
  });
};
