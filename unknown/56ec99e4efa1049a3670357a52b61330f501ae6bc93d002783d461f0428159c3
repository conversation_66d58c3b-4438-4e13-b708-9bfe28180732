import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Typography,
  Textarea,
  DatePicker,
} from '@/shared/components/common';
import { z } from 'zod';
import { CampaignStatus, CampaignType } from '../../types/campaign.types';
import { useSegments, useTemplateEmails } from '../../hooks';

// Schema cho form
const formSchema = z.object({
  name: z.string().min(1, 'Tên chiến dịch là bắt buộc'),
  description: z.string().optional(),
  type: z.nativeEnum(CampaignType, {
    errorMap: () => ({ message: 'Loại chiến dịch là bắt buộc' }),
  }),
  status: z.nativeEnum(CampaignStatus, { errorMap: () => ({ message: 'Trạng thái là bắt buộc' }) }),
  segmentId: z.number({ required_error: '<PERSON>ân đoạn là bắt buộc' }),
  templateId: z.number().optional(),
  startDate: z.string().min(1, '<PERSON><PERSON><PERSON> bắt đầu là bắt buộc'),
  endDate: z.string().optional(),
  content: z.string().optional(),
});

export type CampaignFormValues = z.infer<typeof formSchema>;

interface CampaignFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
}

/**
 * Component form thêm/sửa chiến dịch
 */
const CampaignForm: React.FC<CampaignFormProps> = ({ onSubmit, onCancel }) => {
  const { t } = useTranslation('marketing');
  const { data: segmentsData, isLoading: isSegmentsLoading } = useSegments();
  const { data: templatesData, isLoading: isTemplatesLoading } = useTemplateEmails();

  // Tạo options cho select segment
  const segmentOptions = React.useMemo(() => {
    if (!segmentsData?.items) return [];
    return segmentsData.items.map(segment => ({
      value: segment.id,
      label: segment.name,
    }));
  }, [segmentsData]);

  // Tạo options cho select template
  const templateOptions = React.useMemo(() => {
    if (!templatesData?.items) return [];
    return templatesData.items.map(template => ({
      value: template.id,
      label: template.name,
    }));
  }, [templatesData]);

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        {t('campaign.addNew', 'Thêm chiến dịch mới')}
      </Typography>

      <Form schema={formSchema} onSubmit={onSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="name" label="Tên chiến dịch" required>
            <Input placeholder="Nhập tên chiến dịch" fullWidth />
          </FormItem>

          <FormItem name="type" label="Loại chiến dịch" required>
            <Select
              options={[
                { value: CampaignType.EMAIL, label: 'Email' },
                { value: CampaignType.SMS, label: 'SMS' },
                { value: CampaignType.PUSH, label: 'Thông báo đẩy' },
                { value: CampaignType.SOCIAL, label: 'Mạng xã hội' },
                { value: CampaignType.MULTI_CHANNEL, label: 'Đa kênh' },
              ]}
              placeholder="Chọn loại chiến dịch"
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem name="description" label="Mô tả">
          <Textarea placeholder="Nhập mô tả" rows={3} fullWidth />
        </FormItem>

        <FormItem name="segmentId" label="Phân đoạn" required>
          <Select
            options={segmentOptions}
            placeholder={isSegmentsLoading ? 'Đang tải...' : 'Chọn phân đoạn'}
            fullWidth
            disabled={isSegmentsLoading}
          />
        </FormItem>

        <FormItem name="templateId" label="Mẫu email">
          <Select
            options={templateOptions}
            placeholder={isTemplatesLoading ? 'Đang tải...' : 'Chọn mẫu email'}
            fullWidth
            disabled={isTemplatesLoading}
          />
        </FormItem>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="startDate" label="Ngày bắt đầu" required>
            <DatePicker placeholder="Chọn ngày bắt đầu" fullWidth />
          </FormItem>

          <FormItem name="endDate" label="Ngày kết thúc">
            <DatePicker placeholder="Chọn ngày kết thúc" fullWidth />
          </FormItem>
        </div>

        <FormItem name="content" label="Nội dung">
          <Textarea placeholder="Nhập nội dung" rows={5} fullWidth />
        </FormItem>

        <FormItem name="status" label="Trạng thái" required>
          <Select
            options={[
              { value: CampaignStatus.DRAFT, label: 'Bản nháp' },
              { value: CampaignStatus.SCHEDULED, label: 'Đã lên lịch' },
              { value: CampaignStatus.RUNNING, label: 'Đang chạy' },
              { value: CampaignStatus.PAUSED, label: 'Tạm dừng' },
              { value: CampaignStatus.COMPLETED, label: 'Hoàn thành' },
              { value: CampaignStatus.CANCELLED, label: 'Đã hủy' },
            ]}
            placeholder="Chọn trạng thái"
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onCancel}>
            Hủy
          </Button>
          <Button type="submit" variant="primary">
            Lưu
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CampaignForm;
