import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Input, Select, Button, Icon, IconButton } from '@/shared/components/common';
import { AudienceStatus, AudienceType } from '../../types/audience.types';

interface AudienceFilterProps {
  /**
   * Giá trị filter hiện tại
   */
  filter: AudienceFilterParams;

  /**
   * Callback khi filter thay đổi
   */
  onFilterChange: (filter: Partial<AudienceFilterParams>) => void;
}

/**
 * Component filter cho audience
 */
const AudienceFilter: React.FC<AudienceFilterProps> = ({ filter, onFilterChange }) => {
  const { t } = useTranslation();

  // Danh sách trạng thái
  const statusOptions = [
    { value: '', label: t('common.all', 'Tất cả') },
    { value: AudienceStatus.ACTIVE, label: t('audience.statuses.active', 'Hoạt động') },
    { value: AudienceStatus.INACTIVE, label: t('audience.statuses.inactive', 'Không hoạt động') },
    { value: AudienceStatus.DRAFT, label: t('audience.statuses.draft', 'Bản nháp') },
  ];

  // Danh sách loại
  const typeOptions = [
    { value: '', label: t('common.all', 'Tất cả') },
    { value: AudienceType.CUSTOMER, label: t('audience.types.customer', 'Khách hàng') },
    { value: AudienceType.LEAD, label: t('audience.types.lead', 'Tiềm năng') },
    { value: AudienceType.SUBSCRIBER, label: t('audience.types.subscriber', 'Người đăng ký') },
    { value: AudienceType.CUSTOM, label: t('audience.types.custom', 'Tùy chỉnh') },
  ];

  // Xử lý tìm kiếm khi nhấn Enter
  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onFilterChange({ search: (e.target as HTMLInputElement).value });
    }
  };

  // Xử lý xóa tìm kiếm
  const handleClearSearch = () => {
    onFilterChange({ search: '' });
  };

  // Xử lý reset filter
  const handleResetFilter = () => {
    onFilterChange({
      search: '',
      status: '',
      type: '',
    });
  };

  return (
    <Card className="mb-4 p-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Input
            placeholder={t('common.search', 'Tìm kiếm')}
            value={filter.search || ''}
            onChange={() => {}}
            onKeyDown={handleSearchKeyDown}
            fullWidth
            rightIcon={
              filter.search ? (
                <IconButton icon="x" variant="ghost" size="sm" onClick={handleClearSearch} />
              ) : (
                <Icon name="search" />
              )
            }
          />
        </div>

        <div>
          <Select
            options={statusOptions}
            value={filter.status || ''}
            onChange={value => onFilterChange({ status: value as string })}
            placeholder={t('audience.status', 'Trạng thái')}
            fullWidth
          />
        </div>

        <div>
          <Select
            options={typeOptions}
            value={filter.type || ''}
            onChange={value => onFilterChange({ type: value as string })}
            placeholder={t('audience.type', 'Loại')}
            fullWidth
          />
        </div>
      </div>

      {(filter.search || filter.status || filter.type) && (
        <div className="mt-4 flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={handleResetFilter}
            leftIcon={<Icon name="refresh-cw" />}
          >
            {t('common.resetFilter', 'Đặt lại bộ lọc')}
          </Button>
        </div>
      )}
    </Card>
  );
};

export default AudienceFilter;
