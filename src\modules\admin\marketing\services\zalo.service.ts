// TODO: Implement proper API request utility

import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import type {
  ZaloOAAccountDto,
  ZaloFollowerDto,
  ZaloMessageDto,
  ZNSTemplateDto,
  ZNSMessageDto,
  ZNSCampaignDto,
  CreateZaloOAAccountDto,
  UpdateZaloOAAccountDto,
  CreateZNSTemplateDto,
  SendZNSMessageDto,
  CreateZNSCampaignDto,
  ZaloOAAccountQueryDto,
  ZaloFollowerQueryDto,
  ZaloMessageQueryDto,
  ZNSTemplateQueryDto,
  ConnectOfficialAccountDto,
  OfficialAccountResponseDto,
} from '../types/zalo.types';
import { apiClient } from '@/shared/api';

/**
 * Service cho Zalo Marketing APIs
 */
export class ZaloService {
  private static readonly BASE_URL = '/marketing/zalo';

  /**
   * <PERSON><PERSON><PERSON> danh sách Zalo OA Accounts
   */
  static async getAccounts(query?: ZaloOAAccountQueryDto): Promise<ApiResponseDto<PaginatedResult<ZaloOAAccountDto>>> {
    return apiClient.get(`${this.BASE_URL}`, { params: query });
  }

  /**
   * Lấy chi tiết Zalo OA Account
   */
  static async getAccount(id: number): Promise<ApiResponseDto<ZaloOAAccountDto>> {
    return apiClient.get(`${this.BASE_URL}/accounts/${id}`);
  }

  /**
   * Kết nối Zalo OA Account mới
   */
  static async connectAccount(data: CreateZaloOAAccountDto): Promise<ApiResponseDto<ZaloOAAccountDto>> {
    return apiClient.post(`${this.BASE_URL}/accounts/connect`, data);
  }

  /**
   * Kết nối Official Account với hệ thống
   */
  static async connectOfficialAccount(data: ConnectOfficialAccountDto): Promise<ApiResponseDto<OfficialAccountResponseDto>> {
    return apiClient.post(`${this.BASE_URL}/connect`, data);
  }

  /**
   * Cập nhật Zalo OA Account
   */
  static async updateAccount(id: number, data: UpdateZaloOAAccountDto): Promise<ApiResponseDto<ZaloOAAccountDto>> {
    return apiClient.put(`${this.BASE_URL}/accounts/${id}`, data);
  }

  /**
   * Ngắt kết nối Zalo OA Account
   */
  static async disconnectAccount(id: number): Promise<ApiResponseDto<boolean>> {
    return apiClient.delete(`${this.BASE_URL}/accounts/${id}/disconnect`);
  }

  /**
   * Lấy danh sách followers của OA
   */
  static async getFollowers(oaId: number, query?: ZaloFollowerQueryDto): Promise<ApiResponseDto<PaginatedResult<ZaloFollowerDto>>> {
    return apiClient.get(`${this.BASE_URL}/accounts/${oaId}/followers`, { params: query });
  }

  /**
   * Lấy chi tiết follower
   */
  static async getFollower(oaId: number, followerId: string): Promise<ApiResponseDto<ZaloFollowerDto>> {
    return apiClient.get(`${this.BASE_URL}/accounts/${oaId}/followers/${followerId}`);
  }

  /**
   * Thêm tag cho follower
   */
  static async addTagToFollower(oaId: number, followerId: string, tagName: string): Promise<ApiResponseDto<ZaloFollowerDto>> {
    return apiClient.post(`${this.BASE_URL}/accounts/${oaId}/followers/${followerId}/tags`, { tagName });
  }

  /**
   * Xóa tag của follower
   */
  static async removeTagFromFollower(oaId: number, followerId: string, tagName: string): Promise<ApiResponseDto<ZaloFollowerDto>> {
    return apiClient.delete(`${this.BASE_URL}/accounts/${oaId}/followers/${followerId}/tags/${tagName}`);
  }

  /**
   * Lấy lịch sử tin nhắn với follower
   */
  static async getMessages(oaId: number, followerId: string, query?: ZaloMessageQueryDto): Promise<ApiResponseDto<PaginatedResult<ZaloMessageDto>>> {
    return apiClient.get(`${this.BASE_URL}/accounts/${oaId}/followers/${followerId}/messages`, { params: query });
  }

  /**
   * Gửi tin nhắn đến follower
   */
  static async sendMessage(oaId: number, followerId: string, content: string, type: string = 'text'): Promise<ApiResponseDto<ZaloMessageDto>> {
    return apiClient.post(`${this.BASE_URL}/accounts/${oaId}/messages`, {
      followerId,
      content,
      type,
    });
  }

  /**
   * Gửi tin nhắn broadcast
   */
  static async sendBroadcastMessage(oaId: number, data: {
    content: string;
    type: string;
    targetType: 'ALL' | 'TAGS' | 'CUSTOM';
    targetTags?: string[];
    targetFollowerIds?: string[];
  }): Promise<ApiResponseDto<{ messageId: string; totalRecipients: number }>> {
    return apiClient.post(`${this.BASE_URL}/accounts/${oaId}/broadcast`, data);
  }

  /**
   * Lấy danh sách ZNS templates
   */
  static async getZnsTemplates(query?: ZNSTemplateQueryDto): Promise<ApiResponseDto<PaginatedResult<ZNSTemplateDto>>> {
    return apiClient.get(`${this.BASE_URL}/zns/templates`, { params: query });
  }

  /**
   * Lấy chi tiết ZNS template
   */
  static async getZnsTemplate(id: number): Promise<ApiResponseDto<ZNSTemplateDto>> {
    return apiClient.get(`${this.BASE_URL}/zns/templates/${id}`);
  }

  /**
   * Tạo ZNS template mới
   */
  static async createZnsTemplate(data: CreateZNSTemplateDto): Promise<ApiResponseDto<ZNSTemplateDto>> {
    return apiClient.post(`${this.BASE_URL}/zns/templates`, data);
  }

  /**
   * Cập nhật ZNS template
   */
  static async updateZnsTemplate(id: number, data: Partial<CreateZNSTemplateDto>): Promise<ApiResponseDto<ZNSTemplateDto>> {
    return apiClient.put(`${this.BASE_URL}/zns/templates/${id}`, data);
  }

  /**
   * Xóa ZNS template
   */
  static async deleteZnsTemplate(id: number): Promise<ApiResponseDto<boolean>> {
    return apiClient.delete(`${this.BASE_URL}/zns/templates/${id}`);
  }

  /**
   * Gửi ZNS message
   */
  static async sendZnsMessage(data: SendZNSMessageDto): Promise<ApiResponseDto<ZNSMessageDto>> {
    return apiClient.post(`${this.BASE_URL}/zns/send`, data);
  }

  /**
   * Lấy danh sách ZNS campaigns
   */
  static async getZnsCampaigns(query?: { page?: number; limit?: number; search?: string }): Promise<ApiResponseDto<PaginatedResult<ZNSCampaignDto>>> {
    return apiClient.get(`${this.BASE_URL}/zns/campaigns`, { params: query });
  }

  /**
   * Lấy chi tiết ZNS campaign
   */
  static async getZnsCampaign(id: number): Promise<ApiResponseDto<ZNSCampaignDto>> {
    return apiClient.get(`${this.BASE_URL}/zns/campaigns/${id}`);
  }

  /**
   * Tạo ZNS campaign
   */
  static async createZnsCampaign(data: CreateZNSCampaignDto): Promise<ApiResponseDto<ZNSCampaignDto>> {
    return apiClient.post(`${this.BASE_URL}/zns/campaigns`, data);
  }

  /**
   * Cập nhật ZNS campaign
   */
  static async updateZnsCampaign(id: number, data: Partial<CreateZNSCampaignDto>): Promise<ApiResponseDto<ZNSCampaignDto>> {
    return apiClient.put(`${this.BASE_URL}/zns/campaigns/${id}`, data);
  }

  /**
   * Xóa ZNS campaign
   */
  static async deleteZnsCampaign(id: number): Promise<ApiResponseDto<boolean>> {
    return apiClient.delete(`${this.BASE_URL}/zns/campaigns/${id}`);
  }

  /**
   * Lấy analytics cho Zalo OA
   */
  static async getAnalytics(oaId: number, period: 'TODAY' | 'WEEK' | 'MONTH' | 'YEAR' = 'WEEK'): Promise<ApiResponseDto<{
    totalFollowers: number;
    activeFollowers: number;
    newFollowersToday: number;
    messagesSentToday: number;
    messagesDeliveredToday: number;
    engagementRate: number;
    topTags: Array<{ tag: string; count: number }>;
  }>> {
    return apiClient.get(`${this.BASE_URL}/accounts/${oaId}/analytics`, { params: { period } });
  }

  /**
   * Sync followers từ Zalo API
   */
  static async syncFollowers(oaId: number): Promise<ApiResponseDto<{ syncedCount: number; totalFollowers: number }>> {
    return apiClient.post(`${this.BASE_URL}/accounts/${oaId}/sync-followers`);
  }

  /**
   * Lấy webhook logs
   */
  static async getWebhookLogs(oaId: number, query?: { page?: number; limit?: number }): Promise<ApiResponseDto<PaginatedResult<{
    id: number;
    event: string;
    data: Record<string, unknown>;
    processedAt: number;
    createdAt: number;
  }>>> {
    return apiClient.get(`${this.BASE_URL}/accounts/${oaId}/webhook-logs`, { params: query });
  }

  /**
   * Test webhook connection
   */
  static async testWebhook(oaId: number): Promise<ApiResponseDto<{ success: boolean; message: string }>> {
    return apiClient.post(`${this.BASE_URL}/accounts/${oaId}/test-webhook`);
  }

  /**
   * Bulk operations cho followers
   */
  static async bulkFollowerOperation(oaId: number, data: {
    followerIds: string[];
    operation: 'ADD_TAG' | 'REMOVE_TAG' | 'BLOCK' | 'UNBLOCK';
    tagName?: string;
  }): Promise<ApiResponseDto<{ processedCount: number; failedCount: number }>> {
    return apiClient.post(`${this.BASE_URL}/accounts/${oaId}/followers/bulk`, data);
  }

  /**
   * Export followers data
   */
  static async exportFollowers(oaId: number, format: 'CSV' | 'EXCEL' = 'CSV'): Promise<ApiResponseDto<{ downloadUrl: string }>> {
    return apiClient.post(`${this.BASE_URL}/accounts/${oaId}/followers/export`, { format });
  }
}
