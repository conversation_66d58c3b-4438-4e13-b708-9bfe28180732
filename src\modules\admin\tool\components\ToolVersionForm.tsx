import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Textarea, Select, FormItem } from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import {
  ToolVersion,
  CreateToolVersionParams,
  UpdateToolVersionParams,
  ToolStatus,
} from '../types/tool.types';
import { useAdminTools } from '../hooks';
import { useSetDefaultAdminToolVersion } from '../hooks/useToolVersion';
import JsonEditor from './JsonEditor';

interface ToolVersionFormProps {
  initialValues?: ToolVersion;
  toolId?: string;
  onSubmit: (values: CreateToolVersionParams | UpdateToolVersionParams) => void;
  onCancel: () => void;
  onSetDefault?: (toolId: string, versionId: string) => void;
  readOnly?: boolean;
  isLoading?: boolean;
  isEdit?: boolean;
  showSetDefaultButton?: boolean;
}

/**
 * Component form để tạo/chỉnh sửa phiên bản tool
 */
const ToolVersionForm: React.FC<ToolVersionFormProps> = ({
  initialValues,
  toolId,
  onSubmit,
  onCancel,
  onSetDefault,
  readOnly = false,
  isLoading = false,
  isEdit = false,
  showSetDefaultButton = false,
}) => {
  const { t } = useTranslation(['admin-tool', 'common']);

  // State cho form
  const [toolName, setToolName] = useState(initialValues?.toolName || '');
  const [toolDescription, setToolDescription] = useState(initialValues?.toolDescription || '');
  const [parameters, setParameters] = useState<Record<string, unknown>>(
    initialValues?.parameters || {}
  );
  const [changeDescription, setChangeDescription] = useState(
    initialValues?.changeDescription || ''
  );
  const [status, setStatus] = useState<ToolStatus>(initialValues?.status || ToolStatus.DRAFT);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedToolId, setSelectedToolId] = useState<string>(toolId || '');

  // Lấy danh sách tools để hiển thị trong dropdown nếu đang tạo mới
  const { data: toolsData } = useAdminTools({
    page: 1,
    limit: 100, // Lấy tối đa 100 tools
  });

  // Hook để set default version
  const setDefaultVersionMutation = useSetDefaultAdminToolVersion();

  // Cập nhật state khi có initialValues
  useEffect(() => {
    if (initialValues) {
      setToolName(initialValues.toolName || '');
      setToolDescription(initialValues.toolDescription || '');
      setParameters(initialValues.parameters || {});
      setChangeDescription(initialValues.changeDescription || '');
      setStatus(initialValues.status || ToolStatus.DRAFT);
    }
  }, [initialValues]);

  // Cập nhật toolId khi có thay đổi
  useEffect(() => {
    if (toolId) {
      setSelectedToolId(toolId);
    }
  }, [toolId]);

  // Xử lý thay đổi parameters
  const handleParametersChange = (parsedParams: Record<string, unknown>) => {
    setParameters(parsedParams);
    if (errors['parameters']) {
      setErrors(prev => ({ ...prev, parameters: '' }));
    }
  };

  // Xử lý lỗi JSON
  const handleJsonError = (errorMessage: string | null) => {
    if (errorMessage) {
      setErrors(prev => ({
        ...prev,
        parameters: t('admin-tool:validation.invalidJson', 'Invalid JSON format'),
      }));
    }
  };

  // Xử lý submit form
  const handleSubmit = () => {
    // Validate form
    const newErrors: Record<string, string> = {};

    if (!toolName.trim()) {
      newErrors['toolName'] = t(
        'admin-tool:validation.toolNameRequired',
        'Tên hiển thị tool là bắt buộc'
      );
    }

    if (!selectedToolId && !isEdit) {
      newErrors['toolId'] = t('admin-tool:validation.toolRequired', 'Vui lòng chọn tool');
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Chuẩn bị dữ liệu để submit
    const formData: CreateToolVersionParams | UpdateToolVersionParams = {
      toolName,
      ...(toolDescription && { toolDescription }),
      parameters,
      ...(changeDescription && { changeDescription }),
      status,
    };

    onSubmit(formData);

    // Hiển thị thông báo thành công
    NotificationUtil.success({
      message: isEdit
        ? t('admin-tool:messages.updateVersionSuccess', 'Phiên bản đã được cập nhật thành công!')
        : t('admin-tool:messages.createVersionSuccess', 'Phiên bản mới đã được tạo thành công!'),
      duration: 3000,
    });
  };

  // Xử lý set default version
  const handleSetDefault = () => {
    if (!toolId || !initialValues?.id) return;

    if (onSetDefault) {
      onSetDefault(toolId, initialValues.id);
    } else {
      setDefaultVersionMutation.mutate({
        toolId,
        versionId: initialValues.id,
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Typography variant="h5">
          {isEdit
            ? t('admin-tool:actions.editToolVersion', 'Chỉnh sửa phiên bản tool')
            : t('admin-tool:actions.createToolVersion', 'Tạo phiên bản tool mới')}
        </Typography>
      </div>

      <div className="space-y-4">
        {!isEdit && !toolId && (
          <FormItem
            label={t('admin-tool:form.selectTool', 'Chọn Tool')}
            helpText={errors['toolId']}
            required
          >
            <Select
              value={selectedToolId}
              onChange={value => setSelectedToolId(value as string)}
              options={
                toolsData?.items?.map(tool => ({
                  value: tool.id,
                  label: tool.name,
                })) || []
              }
              disabled={readOnly || isLoading || !!toolId}
              placeholder={t('admin-tool:form.selectToolPlaceholder', 'Chọn tool để tạo phiên bản')}
            />
          </FormItem>
        )}

        <FormItem
          label={t('admin-tool:form.toolName', 'Tên hiển thị')}
          helpText={errors['toolName']}
          required
        >
          <Input
            value={toolName}
            fullWidth
            onChange={e => {
              setToolName(e.target.value);
              if (errors['toolName']) setErrors(prev => ({ ...prev, toolName: '' }));
            }}
            placeholder={t('admin-tool:form.toolNamePlaceholder', 'Nhập tên hiển thị của tool')}
            disabled={readOnly || isLoading}
          />
        </FormItem>

        <FormItem label={t('admin-tool:form.toolDescription', 'Mô tả hiển thị')}>
          <Textarea
            value={toolDescription || ''}
            onChange={e => setToolDescription(e.target.value)}
            placeholder={t(
              'admin-tool:form.toolDescriptionPlaceholder',
              'Nhập mô tả hiển thị của tool'
            )}
            disabled={readOnly || isLoading}
            rows={3}
          />
        </FormItem>

        <FormItem
          label={t('admin-tool:form.parameters', 'Tham số')}
          helpText={errors['parameters']}
          required
        >
          <JsonEditor
            value={parameters}
            onChange={handleParametersChange}
            onError={handleJsonError}
            placeholder={t(
              'admin-tool:form.parametersPlaceholder',
              'Nhập tham số của tool dạng JSON'
            )}
            disabled={readOnly || isLoading}
            minHeight={200}
            className="font-mono text-sm"
          />
        </FormItem>

        <FormItem label={t('admin-tool:form.changeDescription', 'Mô tả thay đổi')}>
          <Textarea
            value={changeDescription || ''}
            onChange={e => setChangeDescription(e.target.value)}
            placeholder={t(
              'admin-tool:form.changeDescriptionPlaceholder',
              'Mô tả những thay đổi trong phiên bản này'
            )}
            disabled={readOnly || isLoading}
            rows={3}
          />
        </FormItem>

        <FormItem label={t('admin-tool:form.statusLabel', 'Trạng thái')}>
          <Select
            value={status}
            onChange={val => setStatus(val as ToolStatus)}
            options={[
              { value: ToolStatus.DRAFT, label: t('admin-tool:status.draft', 'Bản nháp') },
              { value: ToolStatus.APPROVED, label: t('admin-tool:status.approved', 'Đã duyệt') },
              {
                value: ToolStatus.DEPRECATED,
                label: t('admin-tool:status.deprecated', 'Không dùng nữa'),
              },
            ]}
            disabled={readOnly || isLoading}
          />
        </FormItem>
      </div>

      {!readOnly && (
        <div className="flex justify-between items-center pt-4">
          {/* Nút Set Default - chỉ hiển thị khi edit và có showSetDefaultButton */}
          {isEdit && showSetDefaultButton && initialValues?.id && (
            <Button
              variant="secondary"
              onClick={handleSetDefault}
              disabled={isLoading || setDefaultVersionMutation.isPending}
            >
              {setDefaultVersionMutation.isPending
                ? t('common:processing', 'Đang xử lý...')
                : t('admin-tool:actions.setAsDefault', 'Đặt làm mặc định')}
            </Button>
          )}

          {/* Spacer nếu không có nút Set Default */}
          {!(isEdit && showSetDefaultButton && initialValues?.id) && <div />}

          {/* Các nút chính */}
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onCancel} disabled={isLoading}>
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button variant="primary" onClick={handleSubmit} disabled={isLoading}>
              {isLoading ? t('common:processing', 'Đang xử lý...') : t('common:save', 'Lưu')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ToolVersionForm;
