import { z } from 'zod';
import {
  ZaloOAStatus,
  ZNSTemplateStatus,
  ZaloMessageType
} from '../types/zalo.types';

/**
 * Schema cho kết nối Zalo OA Account
 */
export const connectZaloAccountSchema = z.object({
  oaId: z.string().min(1, 'OA ID là bắt buộc'),
  accessToken: z.string().min(1, 'Access token là bắt buộc'),
  refreshToken: z.string().min(1, 'Refresh token là bắt buộc'),
  name: z.string().min(1, 'Tên OA là bắt buộc'),
  avatar: z.string().url().optional(),
});

export type ConnectZaloAccountFormData = z.infer<typeof connectZaloAccountSchema>;

/**
 * Schema cho cập nhật Zalo OA Account
 */
export const updateZaloAccountSchema = z.object({
  name: z.string().min(1, 'Tên OA là bắt buộc').optional(),
  status: z.nativeEnum(ZaloOAStatus).optional(),
});

export type UpdateZaloAccountFormData = z.infer<typeof updateZaloAccountSchema>;

/**
 * Schema cho query Zalo OA Accounts
 */
export const zaloAccountQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  status: z.nativeEnum(ZaloOAStatus).optional(),
  sortBy: z.string().default('createdAt'),
  sortDirection: z.enum(['ASC', 'DESC']).default('DESC'),
});

export type ZaloAccountQueryFormData = z.infer<typeof zaloAccountQuerySchema>;

/**
 * Schema cho query Zalo Followers
 */
export const zaloFollowerQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  oaId: z.number().optional(),
  tags: z.array(z.string()).optional(),
  sortBy: z.string().default('followedAt'),
  sortDirection: z.enum(['ASC', 'DESC']).default('DESC'),
});

export type ZaloFollowerQueryFormData = z.infer<typeof zaloFollowerQuerySchema>;

/**
 * Schema cho gửi tin nhắn Zalo
 */
export const sendZaloMessageSchema = z.object({
  followerId: z.string().min(1, 'Follower ID là bắt buộc'),
  type: z.nativeEnum(ZaloMessageType),
  content: z.string().min(1, 'Nội dung tin nhắn là bắt buộc'),
  attachments: z.array(z.object({
    type: z.string(),
    url: z.string().url(),
    name: z.string().optional(),
    size: z.number().optional(),
  })).optional(),
});

export type SendZaloMessageFormData = z.infer<typeof sendZaloMessageSchema>;

/**
 * Schema cho tạo ZNS Template
 */
export const createZnsTemplateSchema = z.object({
  oaId: z.number().min(1, 'OA ID là bắt buộc'),
  name: z.string().min(1, 'Tên template là bắt buộc'),
  content: z.string().min(1, 'Nội dung template là bắt buộc'),
  params: z.array(z.string()).optional(),
});

export type CreateZnsTemplateFormData = z.infer<typeof createZnsTemplateSchema>;

/**
 * Schema cho gửi ZNS Message
 */
export const sendZnsMessageSchema = z.object({
  oaId: z.number().min(1, 'OA ID là bắt buộc'),
  templateId: z.number().min(1, 'Template ID là bắt buộc'),
  phone: z.string()
    .regex(/^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/, 'Số điện thoại không hợp lệ'),
  params: z.record(z.string()).default({}),
});

export type SendZnsMessageFormData = z.infer<typeof sendZnsMessageSchema>;

/**
 * Schema cho tạo ZNS Campaign
 */
export const createZnsCampaignSchema = z.object({
  oaId: z.number().min(1, 'OA ID là bắt buộc'),
  name: z.string().min(1, 'Tên chiến dịch là bắt buộc'),
  templateId: z.number().min(1, 'Template ID là bắt buộc'),
  phones: z.array(z.string().regex(/^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/))
    .min(1, 'Ít nhất một số điện thoại'),
  params: z.array(z.record(z.string())).default([]),
  scheduleTime: z.number().optional(),
});

export type CreateZnsCampaignFormData = z.infer<typeof createZnsCampaignSchema>;

/**
 * Schema cho query ZNS Templates
 */
export const znsTemplateQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  oaId: z.number().optional(),
  status: z.nativeEnum(ZNSTemplateStatus).optional(),
  sortBy: z.string().default('createdAt'),
  sortDirection: z.enum(['ASC', 'DESC']).default('DESC'),
});

export type ZnsTemplateQueryFormData = z.infer<typeof znsTemplateQuerySchema>;

/**
 * Schema cho query Zalo Messages
 */
export const zaloMessageQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  oaId: z.number().min(1, 'OA ID là bắt buộc'),
  followerId: z.string().min(1, 'Follower ID là bắt buộc'),
  startDate: z.number().optional(),
  endDate: z.number().optional(),
  sortBy: z.string().default('sentAt'),
  sortDirection: z.enum(['ASC', 'DESC']).default('DESC'),
});

export type ZaloMessageQueryFormData = z.infer<typeof zaloMessageQuerySchema>;

/**
 * Schema cho thêm tag cho follower
 */
export const addTagToFollowerSchema = z.object({
  tagName: z.string().min(1, 'Tên tag là bắt buộc'),
});

export type AddTagToFollowerFormData = z.infer<typeof addTagToFollowerSchema>;

/**
 * Schema cho broadcast message
 */
export const broadcastMessageSchema = z.object({
  oaId: z.number().min(1, 'OA ID là bắt buộc'),
  type: z.nativeEnum(ZaloMessageType),
  content: z.string().min(1, 'Nội dung tin nhắn là bắt buộc'),
  targetType: z.enum(['ALL', 'TAGS', 'CUSTOM']),
  targetTags: z.array(z.string()).optional(),
  targetFollowerIds: z.array(z.string()).optional(),
  scheduleTime: z.number().optional(),
});

export type BroadcastMessageFormData = z.infer<typeof broadcastMessageSchema>;

/**
 * Schema validation cho file upload
 */
export const zaloFileUploadSchema = z.object({
  file: z.instanceof(File)
    .refine((file) => file.size <= 10 * 1024 * 1024, 'File không được vượt quá 10MB')
    .refine(
      (file) => ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'].includes(file.type),
      'Loại file không được hỗ trợ'
    ),
});

export type ZaloFileUploadFormData = z.infer<typeof zaloFileUploadSchema>;

/**
 * Schema cho bulk operations
 */
export const bulkFollowerOperationSchema = z.object({
  followerIds: z.array(z.string()).min(1, 'Ít nhất một follower'),
  operation: z.enum(['ADD_TAG', 'REMOVE_TAG', 'BLOCK', 'UNBLOCK']),
  tagName: z.string().optional(),
});

export type BulkFollowerOperationFormData = z.infer<typeof bulkFollowerOperationSchema>;

/**
 * Schema cho kết nối Official Account
 */
export const connectOfficialAccountSchema = z.object({
  connectionName: z.string().min(1, 'Tên kết nối là bắt buộc'),
  accessToken: z.string().min(1, 'Access token là bắt buộc'),
  refreshToken: z.string().min(1, 'Refresh token là bắt buộc'),
  expiresAt: z.number().min(1, 'Thời gian hết hạn là bắt buộc'),
});

export type ConnectOfficialAccountFormData = z.infer<typeof connectOfficialAccountSchema>;
