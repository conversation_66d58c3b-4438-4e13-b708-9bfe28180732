import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import type {
  ShippingProviderConfiguration,
  CreateShippingProviderDto,
  UpdateShippingProviderDto,
  TestShippingProviderDto,
  TestShippingProviderWithConfigDto,
  ShippingProviderQueryParams,
  ShippingProviderTestResult,
  ShippingService,
  ShippingRate,
  ShippingOrder,
  Province,
  District,
  Ward,
  ShippingAddress,
} from '../types';


/**
 * Shipping Provider API Service
 */
export class ShippingProviderService {
  private static readonly BASE_URL = '/user/provider-shipments';

  /**
   * Get all shipping provider configurations
   */
  static async getConfigurations(
    params?: ShippingProviderQueryParams
  ): Promise<ApiResponseDto<PaginatedResult<ShippingProviderConfiguration>>> {
    console.log('🔍 [ShippingProviderService] Calling API:', this.BASE_URL, 'with params:', params);
    const result = await apiClient.get<PaginatedResult<ShippingProviderConfiguration>>(this.BASE_URL, { params });
    console.log('🔍 [ShippingProviderService] API response:', result);
    return result;
  }

  /**
   * Get shipping provider configuration by ID
   */
  static async getConfiguration(id: string): Promise<ApiResponseDto<ShippingProviderConfiguration>> {
    return apiClient.get(`${this.BASE_URL}/${id}`);
  }

  /**
   * Create new shipping provider configuration
   */
  static async createConfiguration(data: CreateShippingProviderDto): Promise<ApiResponseDto<ShippingProviderConfiguration>> {
    return apiClient.post(this.BASE_URL, data);
  }

  /**
   * Update shipping provider configuration
   */
  static async updateConfiguration(
    id: string,
    data: UpdateShippingProviderDto
  ): Promise<ApiResponseDto<ShippingProviderConfiguration>> {
    return apiClient.put(`${this.BASE_URL}/${id}`, data);
  }

  /**
   * Delete shipping provider configuration
   */
  static async deleteConfiguration(id: string): Promise<ApiResponseDto<{ message: string }>> {
    return apiClient.delete(`${this.BASE_URL}/${id}`);
  }

  /**
   * Test shipping provider configuration
   */
  static async testConfiguration(
    id: number,
    testData?: TestShippingProviderDto
  ): Promise<ApiResponseDto<ShippingProviderTestResult>> {
    return apiClient.post(`${this.BASE_URL}/${id}/test`, testData);
  }

  /**
   * Test shipping provider with configuration
   */
  static async testWithConfiguration(
    data: TestShippingProviderWithConfigDto
  ): Promise<ApiResponseDto<ShippingProviderTestResult>> {
    return apiClient.post(`${this.BASE_URL}/test-with-config`, data);
  }

  /**
   * Set provider as default
   */
  static async setAsDefault(id: number): Promise<ApiResponseDto<ShippingProviderConfiguration>> {
    return apiClient.post(`${this.BASE_URL}/${id}/set-default`);
  }

  /**
   * Get available services for a provider
   */
  static async getServices(id: number): Promise<ApiResponseDto<ShippingService[]>> {
    return apiClient.get(`${this.BASE_URL}/${id}/services`);
  }

  /**
   * Calculate shipping rates
   */
  static async calculateRates(
    id: number,
    rateRequest: {
      fromAddress: ShippingAddress;
      toAddress: ShippingAddress;
      weight: number;
      dimensions?: {
        length: number;
        width: number;
        height: number;
      };
    }
  ): Promise<ApiResponseDto<ShippingRate[]>> {
    return apiClient.post(`${this.BASE_URL}/${id}/calculate-rates`, rateRequest);
  }

  /**
   * Create shipping order
   */
  static async createOrder(
    id: number,
    orderData: {
      fromAddress: ShippingAddress;
      toAddress: ShippingAddress;
      weight: number;
      dimensions?: {
        length: number;
        width: number;
        height: number;
      };
      serviceType: string;
      codAmount?: number;
      note?: string;
      requiredNote?: string;
    }
  ): Promise<ApiResponseDto<ShippingOrder>> {
    return apiClient.post(`${this.BASE_URL}/${id}/orders`, orderData);
  }

  /**
   * Track shipping order
   */
  static async trackOrder(
    id: number,
    trackingNumber: string
  ): Promise<ApiResponseDto<ShippingOrder>> {
    return apiClient.get(`${this.BASE_URL}/${id}/orders/${trackingNumber}/track`);
  }

  /**
   * Cancel shipping order
   */
  static async cancelOrder(
    id: number,
    trackingNumber: string
  ): Promise<ApiResponseDto<ShippingOrder>> {
    return apiClient.post(`${this.BASE_URL}/${id}/orders/${trackingNumber}/cancel`);
  }
}

/**
 * Location API Service
 */
export class LocationService {
  private static readonly BASE_URL = '/user/provider-shipments/location';

  /**
   * Get provinces
   */
  static async getProvinces(): Promise<ApiResponseDto<Province[]>> {
    return apiClient.get(`${this.BASE_URL}/provinces`);
  }

  /**
   * Get districts by province
   */
  static async getDistricts(provinceId: number): Promise<ApiResponseDto<District[]>> {
    return apiClient.get(`${this.BASE_URL}/provinces/${provinceId}/districts`);
  }

  /**
   * Get wards by district
   */
  static async getWards(districtId: number): Promise<ApiResponseDto<Ward[]>> {
    return apiClient.get(`${this.BASE_URL}/districts/${districtId}/wards`);
  }
}
