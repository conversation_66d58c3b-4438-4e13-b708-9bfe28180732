/**
 * Service for tag API
 */

import { apiClient } from '@/shared/api';
import {
  CreateTagRequest,
  TagDetailResponse,
  TagListResponse,
  TagQueryParams,
  UpdateTagRequest,
} from '../types/tag.types';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Base URL for tag API
 */
const BASE_URL = '/admin/marketing/tags';

/**
 * Tag service
 */
export const TagService = {
  /**
   * Get all tags with optional filtering
   */
  getTags: async (params?: TagQueryParams): Promise<TagListResponse> => {
    return apiClient.get<TagListResponse['result']>(BASE_URL, { params });
  },

  /**
   * Get tag by ID
   */
  getTagById: async (id: number): Promise<TagDetailResponse> => {
    return apiClient.get<TagDetailResponse['result']>(`${BASE_URL}/${id}`);
  },

  /**
   * Create tag
   */
  createTag: async (data: CreateTagRequest): Promise<TagDetailResponse> => {
    return apiClient.post<TagDetailResponse['result']>(BASE_URL, data);
  },

  /**
   * Update tag
   */
  updateTag: async (id: number, data: UpdateTagRequest): Promise<TagDetailResponse> => {
    return apiClient.put<TagDetailResponse['result']>(`${BASE_URL}/${id}`, data);
  },

  /**
   * Delete tag
   */
  deleteTag: async (id: number): Promise<ApiResponseDto<{ success: boolean }>> => {
    return apiClient.delete<{ success: boolean }>(`${BASE_URL}/${id}`);
  },

  /**
   * Delete multiple tags
   */
  deleteMultipleTags: async (ids: number[]): Promise<ApiResponseDto<{ success: boolean }>> => {
    return apiClient.delete<{ success: boolean }>(BASE_URL, { data: { ids } });
  },
};
