/**
 * Types for template email API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Template email status enum
 */
export enum TemplateEmailStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Template email type enum
 */
export enum TemplateEmailType {
  WELCOME = 'welcome',
  NEWSLETTER = 'newsletter',
  PROMOTION = 'promotion',
  NOTIFICATION = 'notification',
  TRANSACTIONAL = 'transactional',
  CUSTOM = 'custom',
}

/**
 * Template email entity - matches backend TemplateEmailResponseDto
 */
export interface TemplateEmail {
  id: number;
  userId: number;
  name: string;
  subject: string;
  content: string;
  tags: string[];
  placeholders: string[] | Record<string, string>; // Backend can send either array or object
  status?: string; // Backend includes status field
  createdAt: number | string; // Unix timestamp or string
  updatedAt: number | string; // Unix timestamp or string
}

/**
 * Create template email request - matches backend CreateTemplateEmailDto exactly
 */
export interface CreateTemplateEmailRequest {
  name: string;
  subject: string;
  content: string;
  textContent?: string;
  type?: string;
  previewText?: string;
  tags?: string[];
  variables?: Array<{
    name: string;
    type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
    defaultValue?: string;
    required?: boolean;
    description?: string;
  }>;
}

/**
 * Update template email request - matches backend UpdateTemplateEmailDto (now same as CreateTemplateEmailDto)
 */
export interface UpdateTemplateEmailRequest {
  name?: string;
  subject?: string;
  htmlContent?: string;
  textContent?: string;
  type?: string;
  previewText?: string;
  tags?: string[];
  variables?: Array<{
    name: string;
    type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
    defaultValue?: string;
    required?: boolean;
    description?: string;
  }>;
}

/**
 * Template email response
 */
export type TemplateEmailResponse = TemplateEmail;

/**
 * Template email list response
 */
export type TemplateEmailListResponse = ApiResponseDto<PaginatedResult<TemplateEmailResponse>>;

/**
 * Template email detail response
 */
export type TemplateEmailDetailResponse = ApiResponseDto<TemplateEmailResponse>;

/**
 * Template email query params - matches backend TemplateEmailQueryDto
 */
export interface TemplateEmailQueryParams {
  name?: string; // Backend uses 'name' instead of 'search'
  search?: string; // For frontend compatibility
  tag?: string;
  status?: TemplateEmailStatus;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Template email overview response DTO (từ backend API)
 */
export interface TemplateEmailOverviewResponseDto {
  /**
   * Tổng số templates
   */
  totalTemplates: number;

  /**
   * Tổng số template hoạt động
   */
  activeTemplates: number;

  /**
   * Tổng số template bản nháp
   */
  draftTemplates: number;

  /**
   * Tổng số đã gửi test tuần này
   */
  testSentThisWeek: number;

  /**
   * Số template thêm mới tuần này
   */
  newTemplatesThisWeek: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  updatedAt: number;
}

/**
 * Template email overview response
 */
export type TemplateEmailOverviewResponse = ApiResponseDto<TemplateEmailOverviewResponseDto>;
