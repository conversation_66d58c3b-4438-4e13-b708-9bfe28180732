/**
 * Hooks for campaign API using TanStack Query
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { CampaignService } from '../services/campaign.service';
import {
  CampaignQueryParams,
  CreateCampaignRequest,
  UpdateCampaignRequest,
} from '../types/campaign.types';

/**
 * Query keys for campaign API
 */
export const CAMPAIGN_QUERY_KEYS = {
  all: ['marketing', 'campaigns'] as const,
  list: (params: CampaignQueryParams) => [...CAMPAIGN_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...CAMPAIGN_QUERY_KEYS.all, 'detail', id] as const,
  history: (id: number) => [...CAMPAIGN_QUERY_KEYS.all, 'history', id] as const,
};

/**
 * Hook to get campaigns with pagination and filtering
 */
export const useCampaigns = (params: CampaignQueryParams = {}) => {
  return useQuery({
    queryKey: CAMPAIGN_QUERY_KEYS.list(params),
    queryFn: () => CampaignService.getCampaigns(params),
    select: data => data.result,
  });
};

/**
 * Hook to get campaign by ID
 */
export const useCampaign = (id: number) => {
  return useQuery({
    queryKey: CAMPAIGN_QUERY_KEYS.detail(id),
    queryFn: () => CampaignService.getCampaignById(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook to get campaign history
 */
export const useCampaignHistory = (id: number) => {
  return useQuery({
    queryKey: CAMPAIGN_QUERY_KEYS.history(id),
    queryFn: () => CampaignService.getCampaignHistory(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook to create campaign
 */
export const useCreateCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCampaignRequest) => CampaignService.createCampaign(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CAMPAIGN_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to update campaign
 */
export const useUpdateCampaign = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateCampaignRequest) => CampaignService.updateCampaign(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CAMPAIGN_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: CAMPAIGN_QUERY_KEYS.history(id) });
      queryClient.invalidateQueries({ queryKey: CAMPAIGN_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to delete campaign
 */
export const useDeleteCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => CampaignService.deleteCampaign(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CAMPAIGN_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to run campaign
 */
export const useRunCampaign = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => CampaignService.runCampaign(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CAMPAIGN_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: CAMPAIGN_QUERY_KEYS.history(id) });
      queryClient.invalidateQueries({ queryKey: CAMPAIGN_QUERY_KEYS.all });
    },
  });
};
