import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { SettingsState, ChatKeyword, DEFAULT_CHAT_KEYWORDS } from '../types';

// Initial state
const initialState: SettingsState = {
  timezone: 'Asia/Ho_Chi_Minh', // Default to Vietnam timezone
  chatKeywords: DEFAULT_CHAT_KEYWORDS,
  customKeywords: [],
  isLoading: false,
  error: null,
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    // Timezone actions
    setTimezone: (state, action: PayloadAction<string>) => {
      state.timezone = action.payload;
    },

    // Chat keywords actions
    setChatKeywords: (state, action: PayloadAction<ChatKeyword[]>) => {
      state.chatKeywords = action.payload;
    },

    addChatKeyword: (state, action: PayloadAction<ChatKeyword>) => {
      state.customKeywords.push(action.payload);
    },

    updateChatKeyword: (state, action: PayloadAction<{ id: string; updates: Partial<ChatKeyword> }>) => {
      const { id, updates } = action.payload;

      // Update in chatKeywords
      const keywordIndex = state.chatKeywords.findIndex(k => k.id === id);
      if (keywordIndex !== -1 && state.chatKeywords[keywordIndex]) {
        Object.assign(state.chatKeywords[keywordIndex], updates);
      }

      // Update in customKeywords
      const customIndex = state.customKeywords.findIndex(k => k.id === id);
      if (customIndex !== -1 && state.customKeywords[customIndex]) {
        Object.assign(state.customKeywords[customIndex], updates);
      }
    },

    removeChatKeyword: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      
      // Remove from customKeywords only (không xóa default keywords)
      state.customKeywords = state.customKeywords.filter(k => k.id !== id);
      
      // Disable default keyword instead of removing
      const defaultIndex = state.chatKeywords.findIndex(k => k.id === id);
      if (defaultIndex !== -1 && state.chatKeywords[defaultIndex]) {
        state.chatKeywords[defaultIndex].enabled = false;
      }
    },

    toggleChatKeyword: (state, action: PayloadAction<string>) => {
      const id = action.payload;

      // Toggle in chatKeywords
      const keywordIndex = state.chatKeywords.findIndex(k => k.id === id);
      if (keywordIndex !== -1 && state.chatKeywords[keywordIndex]) {
        state.chatKeywords[keywordIndex].enabled = !state.chatKeywords[keywordIndex].enabled;
      }

      // Toggle in customKeywords
      const customIndex = state.customKeywords.findIndex(k => k.id === id);
      if (customIndex !== -1 && state.customKeywords[customIndex]) {
        state.customKeywords[customIndex].enabled = !state.customKeywords[customIndex].enabled;
      }
    },

    resetChatKeywords: (state) => {
      state.chatKeywords = DEFAULT_CHAT_KEYWORDS;
      state.customKeywords = [];
    },

    // Loading and error actions
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    clearError: (state) => {
      state.error = null;
    },

    // Reset all settings
    resetSettings: () => initialState,
  },
});

export const {
  setTimezone,
  setChatKeywords,
  addChatKeyword,
  updateChatKeyword,
  removeChatKeyword,
  toggleChatKeyword,
  resetChatKeywords,
  setLoading,
  setError,
  clearError,
  resetSettings,
} = settingsSlice.actions;

export default settingsSlice.reducer;
