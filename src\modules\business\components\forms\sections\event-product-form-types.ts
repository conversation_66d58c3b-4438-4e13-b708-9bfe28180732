import { FileWithMetadata } from '@/shared/types/file.types';
import { EventTicketType } from '@/modules/business/types/product.types';

// Interface cho trường tùy chỉnh đã chọn
export interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho loại vé trong form (có thêm id và các field khác cho UI)
export interface FormEventTicketType extends Partial<EventTicketType> {
  id: string; // ID tạm thời cho quản lý state
  name: string;
  price: number;
  currency?: string;
  totalTickets?: number;
  saleStartTime?: Date;
  saleEndTime?: Date;
  ticketImage?: string;
  sku?: string;
  minQuantityPerOrder?: number;
  maxQuantityPerOrder?: number;
  description?: string;
}

// Interface cho form values
export interface EventProductFormValues {
  name: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  // Event product specific fields
  eventDateTime?: Date;
  eventLocation?: string;
  attendanceMode: 'ONLINE' | 'OFFLINE';
  zoomLink?: string;
  ticketTypes: FormEventTicketType[];
}

// Props cho các section components
export interface EventGeneralInfoSectionProps {
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface EventInfoSectionProps {}

export interface EventTicketTypesSectionProps {
  ticketTypes: FormEventTicketType[];
  handleAddTicketType: () => void;
  handleRemoveTicketType: (ticketId: string) => void;
  handleUpdateTicketType: (ticketId: string, field: keyof FormEventTicketType, value: string | number | Date) => void;
  handleRemoveTicketImage: (ticketId: string) => void;
}

export interface EventMediaSectionProps {
  mediaFiles: FileWithMetadata[];
  setMediaFiles: (files: FileWithMetadata[]) => void;
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

export interface EventCustomFieldsSectionProps {
  productCustomFields: SelectedCustomField[];
  setProductCustomFields: (fields: SelectedCustomField[]) => void;
  handleToggleCustomFieldToProduct: (fieldId: number, fieldData?: Record<string, unknown>) => void;
  handleUpdateCustomFieldInProduct: (customFieldId: number, value: string | number | boolean) => void;
  handleRemoveCustomFieldFromProduct: (customFieldId: number) => void;
}
