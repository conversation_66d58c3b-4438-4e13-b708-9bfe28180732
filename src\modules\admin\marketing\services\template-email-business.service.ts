/**
 * Template Email Business Service - Layer 2: Business Logic
 * Handles business rules, validation, and data transformation
 */

import { TemplateEmailService } from './template-email.service';
import {
  CreateTemplateEmailRequest,
  UpdateTemplateEmailRequest,
  TemplateEmailQueryParams,
  TemplateEmail,
  TemplateEmailOverviewResponseDto,
} from '../types/template-email.types';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Business service for template emails with validation and business logic
 */
export class TemplateEmailBusinessService {
  /**
   * Get template emails with business logic and validation
   */
  static async getTemplateEmails(
    params?: TemplateEmailQueryParams
  ): Promise<PaginatedResult<TemplateEmail>> {
    // Apply default parameters and business rules
    const defaultParams: TemplateEmailQueryParams = {
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortDirection: 'DESC',
      ...params,
    };

    // Validate parameters
    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit cannot exceed 100 items per page');
    }

    if (defaultParams.page && defaultParams.page < 1) {
      throw new Error('Page number must be greater than 0');
    }

    const response = await TemplateEmailService.getTemplateEmails(defaultParams);
    return response.result;
  }

  /**
   * Get template email by ID with validation
   */
  static async getTemplateEmailById(id: number): Promise<TemplateEmail> {
    if (!id || id <= 0) {
      throw new Error('Invalid template email ID');
    }

    const response = await TemplateEmailService.getTemplateEmailById(id);
    return response.result;
  }

  /**
   * Create template email with validation and business logic
   */
  static async createTemplateEmail(data: CreateTemplateEmailRequest): Promise<TemplateEmail> {
    // Validate required fields
    if (!data.name?.trim()) {
      throw new Error('Template name is required');
    }

    if (!data.subject?.trim()) {
      throw new Error('Template subject is required');
    }

    if (!data.content?.trim()) {
      throw new Error('Template content is required');
    }

    // Apply business rules
    const processedData: CreateTemplateEmailRequest = {
      ...data,
      name: data.name.trim(),
      subject: data.subject.trim(),
      content: data.content.trim(),
      textContent: data.textContent?.trim(),
      type: data.type || 'NEWSLETTER',
      previewText: data.previewText?.trim(),
      tags: data.tags?.filter(tag => tag.trim()) || [],
      variables: data.variables || [],
    };



    // Validate name length
    if (processedData.name.length > 255) {
      throw new Error('Template name cannot exceed 255 characters');
    }

    // Validate subject length
    if (processedData.subject.length > 255) {
      throw new Error('Template subject cannot exceed 255 characters');
    }

    const response = await TemplateEmailService.createTemplateEmail(processedData);
    return response.result;
  }

  /**
   * Update template email with validation and business logic
   */
  static async updateTemplateEmail(
    id: number,
    data: UpdateTemplateEmailRequest
  ): Promise<TemplateEmail> {
    if (!id || id <= 0) {
      throw new Error('Invalid template email ID');
    }

    // Validate and process data
    const processedData: UpdateTemplateEmailRequest = { ...data };

    if (data.name !== undefined) {
      if (!data.name.trim()) {
        throw new Error('Template name cannot be empty');
      }
      if (data.name.length > 255) {
        throw new Error('Template name cannot exceed 255 characters');
      }
      processedData.name = data.name.trim();
    }

    if (data.subject !== undefined) {
      if (!data.subject.trim()) {
        throw new Error('Template subject cannot be empty');
      }
      if (data.subject.length > 255) {
        throw new Error('Template subject cannot exceed 255 characters');
      }
      processedData.subject = data.subject.trim();
    }

    if (data.htmlContent !== undefined) {
      if (!data.htmlContent.trim()) {
        throw new Error('Template HTML content cannot be empty');
      }
      processedData.htmlContent = data.htmlContent.trim();
    }

    if (data.textContent !== undefined) {
      processedData.textContent = data.textContent?.trim();
    }

    if (data.previewText !== undefined) {
      processedData.previewText = data.previewText?.trim();
    }

    if (data.tags !== undefined) {
      processedData.tags = data.tags.filter(tag => tag.trim());
    }

    if (data.variables !== undefined) {
      processedData.variables = data.variables.filter(variable => variable.name.trim());
    }

    const response = await TemplateEmailService.updateTemplateEmail(id, processedData);
    return response.result;
  }

  /**
   * Delete template email with validation
   */
  static async deleteTemplateEmail(id: number): Promise<boolean> {
    if (!id || id <= 0) {
      throw new Error('Invalid template email ID');
    }

    const response = await TemplateEmailService.deleteTemplateEmail(id);
    return response.result.success;
  }

  /**
   * Bulk delete template emails with validation
   */
  static async bulkDeleteTemplateEmails(ids: number[]): Promise<boolean> {
    if (!ids || ids.length === 0) {
      throw new Error('No template IDs provided for deletion');
    }

    // Validate all IDs
    const invalidIds = ids.filter(id => !id || id <= 0);
    if (invalidIds.length > 0) {
      throw new Error(`Invalid template email IDs: ${invalidIds.join(', ')}`);
    }

    // Limit bulk operations to prevent abuse
    if (ids.length > 50) {
      throw new Error('Cannot delete more than 50 templates at once');
    }

    const response = await TemplateEmailService.bulkDeleteTemplateEmails(ids);
    return response.result.success;
  }

  /**
   * Validate template content for placeholders
   */
  static validateTemplatePlaceholders(content: string, placeholders: string[]): {
    isValid: boolean;
    missingPlaceholders: string[];
    unusedPlaceholders: string[];
  } {
    const contentPlaceholders = this.extractPlaceholdersFromContent(content);
    const missingPlaceholders = placeholders.filter(
      placeholder => !contentPlaceholders.includes(placeholder)
    );
    const unusedPlaceholders = contentPlaceholders.filter(
      placeholder => !placeholders.includes(placeholder)
    );

    return {
      isValid: missingPlaceholders.length === 0 && unusedPlaceholders.length === 0,
      missingPlaceholders,
      unusedPlaceholders,
    };
  }

  /**
   * Extract placeholders from template content
   */
  private static extractPlaceholdersFromContent(content: string): string[] {
    const placeholderRegex = /\{\{(\w+)\}\}/g;
    const matches = content.match(placeholderRegex);
    return matches ? matches.map(match => match.replace(/[{}]/g, '')) : [];
  }

  /**
   * Preview template with placeholder values
   */
  static previewTemplate(
    content: string,
    placeholderValues: Record<string, string>
  ): string {
    let previewContent = content;
    
    Object.entries(placeholderValues).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      previewContent = previewContent.replace(new RegExp(placeholder, 'g'), value);
    });

    return previewContent;
  }

  /**
   * Get template statistics
   */
  static async getTemplateStatistics(): Promise<{
    totalTemplates: number;
    activeTemplates: number;
    draftTemplates: number;
    recentTemplates: TemplateEmail[];
  }> {
    // Get all templates for statistics
    const allTemplatesResponse = await this.getTemplateEmails({ limit: 100 });
    const recentTemplatesResponse = await this.getTemplateEmails({
      limit: 5,
      sortBy: 'createdAt',
      sortDirection: 'DESC'
    });

    return {
      totalTemplates: allTemplatesResponse.meta.totalItems,
      activeTemplates: allTemplatesResponse.items.length, // All templates are considered active in current backend
      draftTemplates: 0, // No draft status in current backend
      recentTemplates: recentTemplatesResponse.items,
    };
  }

  /**
   * Get template email overview statistics
   */
  static async getOverview(): Promise<TemplateEmailOverviewResponseDto> {
    const response = await TemplateEmailService.getOverview();
    return response.result;
  }
}
