{"admin-tool": {"title": "Tool Management", "description": "Manage tools in the system, including add, edit, delete and approve tools", "tools": "Tools", "toolsDescription": "Manage tools in the system, including add, edit, delete and approve tools.", "trash": {"titleTrash": "Trash", "trashDescription": "Manage soft-deleted tools, including restore and permanently delete tools.", "name": "Tool Name", "description1": "Description", "status": "Status", "deletedAt": "Deleted At", "deletedBy": "Deleted By", "rollback": "Rest<PERSON>", "rollbackSuccess": "Tool restored successfully", "rollbackError": "Error restoring tool"}, "menu": {"overview": "Overview", "tools": "Tools", "trash": "Trash"}, "form": {"name": "Tool Name", "namePlaceholder": "Enter tool name", "description": "Description", "description1": "Description", "descriptionPlaceholder": "Enter tool description", "statusLabel": "Status", "accessType": "Access Type", "toolName": "Tool Display Name", "toolNamePlaceholder": "Enter tool version name", "toolDescription": "Tool Version Description", "toolDescriptionVersion": "Tool Version Description", "toolDescriptionPlaceholder": "Enter tool version description", "parameters": "Tool Parameters", "parametersPlaceholder": "Enter tool parameters in JSON format", "changeDescription": "Change Description", "changeDescriptionPlaceholder": "Describe the changes in this version", "versionStatus": "Version Status", "selectTool": "Select Tool", "selectToolPlaceholder": "Select tool to create version"}, "status": {"draft": "Draft", "approved": "Approved", "deprecated": "Deprecated"}, "access": {"public": "Public", "private": "Private", "restricted": "Restricted"}, "actions": {"create": "Create", "update": "Update", "creating": "Creating...", "updating": "Updating...", "viewTool": "View Tool", "editTool": "<PERSON>l", "editTool1": "<PERSON>l", "createTool": "Create New Tool", "createToolVersion": "Create New Tool Version", "editToolVersion": "Edit Tool Version", "addVersion": "Add Version", "deleteVersion": "Delete version", "setAsDefault": "Set as <PERSON><PERSON><PERSON>"}, "validation": {"nameRequired": "Tool name is required", "toolNameRequired": "Tool display name is required", "parametersRequired": "Tool parameters are required", "invalidJson": "Invalid JSON format", "toolRequired": "Please select a tool"}, "messages": {"noDescription": "No description", "createdBy": "Created by", "unknownUser": "Unknown user", "noVersions": "No versions available. Click \"Add Version\" to create the first version.", "loadingVersionDetail": "Loading version details...", "selectToolFirst": "Please select a tool first", "updateVersionSuccess": "Version has been updated successfully!", "createVersionSuccess": "New version has been created successfully!", "confirmDeleteVersion": "Confirm Delete Version", "confirmDeleteVersionMessage": "Are you sure you want to delete this version?", "noTools": "No tools found"}, "table": {"columns": {"name": "Name", "description": "Description", "status": "Status", "accessType": "Access Type", "createdBy": "Created By", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions"}}, "filters": {"all": "All", "draft": "Draft", "approved": "Approved", "deprecated": "Deprecated", "hasUpdate": "Has Update", "noUpdate": "No Update"}, "notifications": {"createSuccess": {"title": "Success", "message": "Tool has been created successfully!"}, "updateSuccess": {"title": "Success", "message": "Tool has been updated successfully!"}, "deleteSuccess": {"title": "Success", "message": "Tool has been deleted successfully!"}, "restoreSuccess": {"title": "Success", "message": "Tool has been restored successfully!"}, "updateVersionSuccess": {"title": "Success", "message": "Version has been updated successfully!"}, "createVersionSuccess": {"title": "Success", "message": "New version has been created successfully!"}, "deleteVersionSuccess": {"title": "Success", "message": "Version has been deleted successfully!"}, "error": {"title": "Error", "message": "An error occurred. Please try again."}}, "confirmDelete": {"title": "Confirm Delete", "message": "Are you sure you want to delete this tool? This action cannot be undone."}, "confirmRestore": {"title": "Confirm <PERSON>ore", "message": "Are you sure you want to restore this tool?"}}}