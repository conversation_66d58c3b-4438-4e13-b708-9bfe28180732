import { useQuery } from '@tanstack/react-query';
import {
  ProductListResponse,
  ProductCategoryDisplay,
  ProductBrand,
} from '../types/product.types';
import { ProductService } from '../services';

import { transformApiProductsToListItems, getDisplayCategories, getCategoryEnumBySlug } from '../utils/data-transformer';
import { PRODUCT_QUERY_KEYS } from '../constants/product-query-keys';

interface UseProductListProps {
  page: number;
  limit: number;
  search?: string;
  category?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'price' | 'rating' | 'newest';
  sortOrder?: 'asc' | 'desc';
}

interface UseProductListReturn {
  data: ProductListResponse | null;
  isLoading: boolean;
  error: Error | null;
  categories: ProductCategoryDisplay[];
  brands: ProductBrand[];
  priceRange: {
    min: number;
    max: number;
  };
  refetch: () => void;
}

// Mock brands (vẫn giữ để tương thích UI)
const mockBrands: ProductBrand[] = [
  { id: 'redai', name: 'RedAI', logo: '/images/redai-logo.png' },
  { id: 'community', name: 'Community', logo: '/images/community-logo.png' },
];

// Default price range
const DEFAULT_PRICE_RANGE = {
  min: 0,
  max: 10000000
};

/**
 * Hook để quản lý trạng thái và logic cho trang danh sách sản phẩm
 * Sử dụng real API thay vì mock data
 */
export const useProductList = ({
  page,
  limit,
  search,
  category,
  minPrice,
  maxPrice,
  sortBy = 'newest',
  sortOrder = 'desc',
}: Omit<UseProductListProps, 'brand'>): UseProductListReturn => {

  // Xử lý category - nếu là enum value thì dùng trực tiếp, nếu là slug thì convert
  let categoryForApi: string | undefined;
  if (category) {
    // Kiểm tra xem category có phải là enum value không
    const validEnums = ['AGENT', 'KNOWLEDGE_FILE', 'FUNCTION', 'FINETUNE', 'STRATEGY'];
    if (validEnums.includes(category)) {
      categoryForApi = category;
    } else {
      // Nếu là slug thì convert sang enum
      categoryForApi = getCategoryEnumBySlug(category);
    }
  }

  // Tạo query parameters cho API
  const queryParams: any = {
    page,
    limit,
    sortBy: sortBy === 'newest' ? 'createdAt' : sortBy,
    sortDirection: sortOrder.toUpperCase() as 'ASC' | 'DESC',
  };

  // Thêm search nếu có
  if (search) {
    queryParams.search = search;
  }

  // Thêm category nếu có
  if (categoryForApi) {
    queryParams.category = categoryForApi;
  }

  // Thêm minPrice và maxPrice nếu có
  if (minPrice !== undefined) {
    queryParams.minPrice = minPrice;
  }

  if (maxPrice !== undefined) {
    queryParams.maxPrice = maxPrice;
  }

  // Gọi API để lấy danh sách sản phẩm
  const {
    data: apiResponse,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: [PRODUCT_QUERY_KEYS.LIST, queryParams],
    queryFn: () => ProductService.getApprovedProducts(queryParams),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Transform dữ liệu từ API sang format UI
  const transformedData: ProductListResponse | null = apiResponse ? {
    data: transformApiProductsToListItems(apiResponse.items || []),
    total: apiResponse.meta?.totalItems || 0,
    page: apiResponse.meta?.currentPage || 1,
    limit: apiResponse.meta?.itemsPerPage || 10,
  } : null;

  // Lấy danh sách categories từ transformer
  const categories = getDisplayCategories();

  return {
    data: transformedData,
    isLoading,
    error: error as Error | null,
    categories,
    brands: mockBrands,
    priceRange: DEFAULT_PRICE_RANGE,
    refetch,
  };
};
