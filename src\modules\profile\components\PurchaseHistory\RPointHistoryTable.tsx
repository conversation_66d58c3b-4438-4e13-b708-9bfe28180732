import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Table, TableColumn } from '@/shared/components/common/Table';
import { Tooltip, IconCard, SearchBar, Button, Checkbox } from '@/shared/components/common';
import { ModernMenu } from '@/shared/components/common';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';

// Import hook và types từ module rpoint
import { useRPointPurchaseHistory } from '@/modules/rpoint/hooks/useRPointQuery';
import {
  PurchaseHistoryQueryParams,
  TransactionStatus,
} from '@/modules/rpoint/types/rpoint-api.types';

// Import hình ảnh R-Point
import rpointImage from '@/shared/assets/images/rpoint.png';

// Interface cho dữ liệu lịch sử R-Point trong component
interface RPointHistoryItemDisplay {
  id: number;
  createdAt: string;
  points: number;
  amount: number;
  status: TransactionStatus;
  invoice?: string;
}

// Interface cho cài đặt hiển thị cột
interface ColumnVisibility {
  id: string;
  label: string;
  visible: boolean;
}

/**
 * Component hiển thị bảng lịch sử nạp R-Point
 */
const RPointHistoryTable: React.FC = () => {
  const { t } = useTranslation('profile');
  const [searchText, setSearchText] = useState('');
  const [queryParams, setQueryParams] = useState<PurchaseHistoryQueryParams>({
    page: 1,
    limit: 5,
  });
  const [showSearch, setShowSearch] = useState(false);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [showColumnMenu, setShowColumnMenu] = useState(false);
  const [columnSettings, setColumnSettings] = useState<ColumnVisibility[]>([
    { id: 'all', label: t('common.selectAll', 'Chọn tất cả'), visible: true },
    { id: 'id', label: 'ID', visible: true },
    { id: 'createdAt', label: t('purchaseHistory.createdAt', 'Ngày tạo'), visible: true },
    { id: 'points', label: t('purchaseHistory.points', 'Số point'), visible: true },
    { id: 'amount', label: t('purchaseHistory.amount', 'Thanh toán'), visible: true },
    { id: 'status', label: t('purchaseHistory.status', 'Trạng thái'), visible: true },
    { id: 'invoice', label: t('purchaseHistory.invoice', 'Hóa đơn'), visible: true },
  ]);

  // Gọi API lấy lịch sử mua R-Point
  const { data, isLoading } = useRPointPurchaseHistory(queryParams);

  // Chuyển đổi dữ liệu từ API sang định dạng hiển thị
  const historyData = useMemo(() => {
    if (!data) {
      return {
        items: [],
        total: 0,
      };
    }

    return {
      items: data.items.map(item => ({
        id: item.id,
        createdAt: new Date(item.completedAt).toLocaleDateString('vi-VN'),
        points: item.pointsAmount,
        amount: item.amount,
        status: item.status,
        invoice: item.id.toString(), // Sử dụng ID làm tên file hóa đơn
      })),
      total: data.meta.totalItems,
    };
  }, [data]);

  // Không cần hàm handlePageChange riêng vì đã xử lý trong pagination của Table

  // Xử lý khi thay đổi từ khóa tìm kiếm
  const handleSearchChange = (value: string) => {
    setSearchText(value);
    setQueryParams(prev => ({ ...prev, page: 1, search: value }));
  };

  // Không cần danh sách tùy chọn cột vì đã sử dụng columnSettings

  // Định nghĩa các cột cho bảng
  const allColumns: TableColumn<RPointHistoryItemDisplay>[] = useMemo(
    () => [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: '10%',
        sorter: true,
      },
      {
        title: t('purchaseHistory.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: '15%',
        sorter: true,
      },
      {
        title: (
          <div className="flex items-center">
            <span>{t('purchaseHistory.points', 'Số point')}</span>
            <img src={rpointImage} alt="R-Point" className="ml-1 w-4 h-4" />
          </div>
        ),
        dataIndex: 'points',
        key: 'points',
        width: '15%',
        sorter: true,
        render: (value: unknown) => (
          <div className="text-left">
            <span>{(value as number).toLocaleString()}</span>
          </div>
        ),
      },
      {
        title: <div className="text-right">{t('purchaseHistory.amount', 'Thanh toán')}</div>,
        dataIndex: 'amount',
        key: 'amount',
        width: '15%',
        sorter: true,
        render: (value: unknown) => (
          <div className="text-right">{(value as number).toLocaleString()}</div>
        ),
      },
      {
        title: t('purchaseHistory.status', 'Trạng thái'),
        dataIndex: 'status',
        key: 'status',
        width: '15%',
        sorter: true,
        render: (value: unknown) => {
          const status = value as TransactionStatus;
          return (
            <div
              className={`text-${status === TransactionStatus.COMPLETED ? 'red-500' : 'yellow-500'}`}
            >
              {status === TransactionStatus.COMPLETED
                ? t('purchaseHistory.statusCompleted', 'Đã thanh toán')
                : t('purchaseHistory.statusPending', 'Chờ thanh toán')}
            </div>
          );
        },
      },
      {
        title: t('purchaseHistory.invoice', 'Hóa đơn'),
        dataIndex: 'invoice',
        key: 'invoice',
        width: '15%',
        render: (value: unknown, record: RPointHistoryItemDisplay) => {
          if (record.status === TransactionStatus.COMPLETED && value) {
            return (
              <Button
                variant="primary"
                size="sm"
                onClick={() => console.log(`Downloading invoice: ${value}`)}
              >
                {t('purchaseHistory.download', 'Tải xuống')}
              </Button>
            );
          }
          return (
            <div className="text-yellow-500">{t('purchaseHistory.processing', 'Chờ xử lý')}</div>
          );
        },
      },
    ],
    [t]
  );

  // Lọc các cột theo columnSettings
  const columns = useMemo(() => {
    const visibleColumnIds = columnSettings
      .filter(col => col.visible && col.id !== 'all')
      .map(col => col.id);
    return allColumns.filter(column => visibleColumnIds.includes(column.key));
  }, [allColumns, columnSettings]);

  // Xử lý khi click vào nút tìm kiếm
  const handleSearchClick = () => {
    setShowSearch(!showSearch);
    if (showSearch && searchText) {
      setSearchText('');
      setQueryParams(prev => ({ ...prev, page: 1, search: '' }));
    }
  };

  // Xử lý khi click vào nút lọc
  const handleFilterClick = () => {
    setShowFilterMenu(!showFilterMenu);
  };

  // Danh sách các mục lọc
  const filterItems: ModernMenuItem[] = [
    {
      id: 'all',
      label: t('common.all', 'Tất cả'),
      icon: 'list',
      onClick: () => {
        // Xử lý khi chọn tất cả
      },
    },
    {
      id: 'completed',
      label: t('purchaseHistory.statusCompleted', 'Đã thanh toán'),
      icon: 'check-circle',
      onClick: () => {
        // Xử lý khi chọn đã thanh toán
      },
    },
    {
      id: 'pending',
      label: t('purchaseHistory.statusPending', 'Chờ thanh toán'),
      icon: 'clock',
      onClick: () => {
        // Xử lý khi chọn chờ thanh toán
      },
    },
  ];

  return (
    <div className="space-y-4">
      {/* Bộ lọc và tìm kiếm */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3 mb-4">
        <div className="flex items-center space-x-2">
          <Tooltip content={t('common.search', 'Tìm kiếm')} position="bottom">
            <IconCard
              icon="search"
              variant={showSearch ? 'primary' : 'default'}
              onClick={handleSearchClick}
              active={showSearch}
            />
          </Tooltip>

          <div className="relative">
            <Tooltip content={t('common.filter', 'Lọc')} position="right">
              <IconCard icon="filter" variant="default" onClick={handleFilterClick} />

              {showFilterMenu && (
                <ModernMenu
                  isOpen={showFilterMenu}
                  onClose={() => setShowFilterMenu(false)}
                  placement="bottom"
                  width="180px"
                  items={filterItems}
                />
              )}
            </Tooltip>
          </div>

          <div className="relative">
            <Tooltip content={t('common.columns', 'Cột hiển thị')} position="right">
              <IconCard
                icon="layers"
                variant="default"
                onClick={() => setShowColumnMenu(!showColumnMenu)}
              />

              {showColumnMenu && (
                <ModernMenu
                  isOpen={showColumnMenu}
                  onClose={() => setShowColumnMenu(false)}
                  placement="bottom"
                  width="220px"
                  items={[
                    {
                      id: 'header',
                      label: (
                        <div className="font-medium text-sm mb-1">
                          {t('common.columns', 'Cột hiển thị')}
                        </div>
                      ),
                      divider: true,
                    },
                    {
                      id: 'all',
                      label: (
                        <div className="flex items-center">
                          <Checkbox
                            id="column-all"
                            checked={columnSettings.find(col => col.id === 'all')?.visible ?? false}
                            onChange={checked => {
                              const updatedColumns = columnSettings.map(col => ({
                                ...col,
                                visible: checked,
                              }));
                              setColumnSettings(updatedColumns);
                            }}
                            variant="filled"
                            size="sm"
                            label={
                              <span className="ml-1 text-sm">
                                {t('common.selectAll', 'Chọn tất cả')}
                              </span>
                            }
                          />
                        </div>
                      ),
                    },
                    ...columnSettings
                      .filter(column => column.id !== 'all')
                      .map(column => ({
                        id: column.id,
                        label: (
                          <div className="flex items-center">
                            <Checkbox
                              id={`column-${column.id}`}
                              checked={column.visible}
                              onChange={checked => {
                                const updatedColumns = [...columnSettings];
                                const columnToUpdate = updatedColumns.find(
                                  col => col.id === column.id
                                );
                                if (columnToUpdate) {
                                  columnToUpdate.visible = checked;
                                }

                                // Cập nhật trạng thái "Chọn tất cả"
                                const allColumn = updatedColumns.find(col => col.id === 'all');
                                if (allColumn) {
                                  const otherColumns = updatedColumns.filter(
                                    col => col.id !== 'all'
                                  );
                                  allColumn.visible = otherColumns.every(col => col.visible);
                                }

                                setColumnSettings(updatedColumns);
                              }}
                              variant="filled"
                              size="sm"
                              label={<span className="ml-1 text-sm">{column.label}</span>}
                            />
                          </div>
                        ),
                      })),
                  ]}
                />
              )}
            </Tooltip>
          </div>
        </div>

        {/* Chỉ hiển thị SearchBar khi showSearch = true */}
        <div className="w-full sm:w-auto">
          <SearchBar
            visible={showSearch}
            value={searchText}
            onChange={handleSearchChange}
            onToggle={handleSearchClick}
            maxWidth="100%"
            variant="flat"
            autoFocus={true}
            showSearchIcon={false}
            className="w-full"
            placeholder={t('purchaseHistory.searchPlaceholder', 'Tìm kiếm theo ID hoặc ngày mua')}
          />
        </div>
      </div>

      <Table
        columns={columns}
        data={historyData.items}
        rowKey="id"
        loading={isLoading}
        sortable={true}
        pagination={{
          total: historyData.total,
          current: queryParams.page || 1,
          pageSize: queryParams.limit || 5,
          onChange: (page, pageSize) => {
            if (pageSize !== queryParams.limit) {
              setQueryParams(prev => ({ ...prev, page: 1, limit: pageSize }));
            } else {
              setQueryParams(prev => ({ ...prev, page }));
            }
          },
          pageSizeOptions: [5, 10, 20, 50],
          showSizeChanger: true,
          showFirstLastButtons: true,
        }}
        bordered
        hoverable
      />
    </div>
  );
};

export default RPointHistoryTable;
