import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FieldValues, Controller } from 'react-hook-form';

// UI Components
import { Card, Typography, Input, Form, FormItem, DateTimePicker } from '@/shared/components/common';
import { IconCard } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';

// Hooks and Services
import { useConnectOfficialAccount } from '../../hooks/zalo/useZaloConnect';
import { connectOfficialAccountSchema, type ConnectOfficialAccountFormData } from '../../schemas/zalo.schema';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang kết nối Zalo Official Account với hệ thống
 */
const ZaloOfficialAccountConnectPage: React.FC = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<FormRef<FieldValues>>(null);

  // Hook kết nối Official Account
  const connectMutation = useConnectOfficialAccount();

  // Handle form submission
  const onSubmit = async (data: any) => {
    setIsSubmitting(true);

    try {
      const formData = data as ConnectOfficialAccountFormData;
      const response = await connectMutation.mutateAsync(formData);
      
      if (response.code === 0 && response.result) {
        NotificationUtil.success({
          message: 'Kết nối Official Account thành công!'
        });

        // Redirect to accounts page or dashboard
        navigate('/marketing/zalo/accounts');
      } else {
        NotificationUtil.error({
          message: response.message || 'Có lỗi xảy ra khi kết nối'
        });
      }
    } catch (error: unknown) {
      console.error('Lỗi kết nối Official Account:', error);
      const errorMessage = error instanceof Error ? error.message : 'Không thể kết nối Official Account';
      NotificationUtil.error({
        message: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate(-1); // Go back to previous page
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Form Section */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <div className="space-y-6">
                <div>
                  <Typography variant="h2" className="text-xl font-semibold mb-2">
                    Thông tin kết nối
                  </Typography>
                  <Typography variant="body2" className="text-muted-foreground">
                    Nhập thông tin access token và refresh token
                  </Typography>
                </div>

                <Form
                  ref={formRef}
                  schema={connectOfficialAccountSchema}
                  onSubmit={onSubmit}
                  defaultValues={{
                    connectionName: '',
                    accessToken: '',
                    refreshToken: '',
                    expiresAt: Date.now() + 30 * 24 * 60 * 60 * 1000, // Default to 30 days from now (timestamp)
                  }}
                >
                  <div className="space-y-6">
                    {/* Connection Name */}
                    <FormItem
                      name="connectionName"
                      label="Tên kết nối *"
                      required
                    >
                      <Input
                        placeholder="Nhập tên cho kết nối này"
                        disabled={isSubmitting}
                        fullWidth
                      />
                    </FormItem>

                    {/* Access Token */}
                    <FormItem
                      name="accessToken"
                      label="Access Token *"
                      required
                    >
                      <Input
                        placeholder="Nhập access token"
                        type="text"
                        disabled={isSubmitting}
                        fullWidth
                      />
                    </FormItem>

                    {/* Refresh Token */}
                    <FormItem
                      name="refreshToken"
                      label="Refresh Token *"
                      required
                    >
                      <Input
                        placeholder="Nhập refresh token"
                        type="text"
                        disabled={isSubmitting}
                        fullWidth
                      />
                    </FormItem>

                    {/* Expires At */}
                    <FormItem
                      name="expiresAt"
                      label="Thời gian hết hạn"
                      required
                    >
                      <Controller
                        name="expiresAt"
                        render={({ field: { value, onChange } }) => (
                          <DateTimePicker
                            value={value instanceof Date ? value : value ? new Date(value) : null}
                            onChange={(date) => {
                              onChange(date ? date.getTime() : 0);
                            }}
                            placeholder="Chọn ngày và giờ hết hạn"
                            disabled={isSubmitting}
                            fullWidth
                            format="dd/MM/yyyy HH:mm"
                            minDate={new Date()}
                          />
                        )}
                      />
                    </FormItem>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-4 pt-6">
                      <IconCard
                        icon="x"
                        title="Hủy"
                        onClick={handleCancel}
                        className="cursor-pointer"
                        disabled={isSubmitting}
                      />
                      <IconCard
                        icon="check"
                        title="Lưu"
                        onClick={() => formRef.current?.submit()}
                        variant="primary"
                        disabled={isSubmitting}
                        className="cursor-pointer"
                      />
                    </div>
                  </div>
                </Form>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ZaloOfficialAccountConnectPage;

