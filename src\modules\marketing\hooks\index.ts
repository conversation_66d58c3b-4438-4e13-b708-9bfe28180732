/**
 * Export all hooks
 */

export * from './useTagQuery';
export * from './useAudienceQuery';
export * from './useSegmentQuery';
export * from './useCampaignQuery';
export * from './useStatisticsQuery';
export * from './useTemplateEmailQuery';
export * from './useCustomFieldQuery';
export * from './useMarketingOverview';

// Email hooks
export * from './email/useEmailTemplates';
export * from './email/useEmailCampaigns';

// Google Ads hooks
export { default as useGoogleAdsAccounts } from './google-ads/useGoogleAdsAccounts';
export { default as useGoogleAdsCampaigns } from './google-ads/useGoogleAdsCampaigns';

// Facebook Ads hooks
export * from './facebook-ads/useFacebookAdsAccounts';
export * from './facebook-ads/useFacebookAdsCampaigns';
export * from './facebook-ads/useFacebookAuth';

// Zalo hooks
export * from './zalo/useZaloConnect';
