import { z } from 'zod';
import { SegmentStatus, ConditionOperator } from '../types/segment.types';

/**
 * Schema validation cho điều kiện segment
 */
export const segmentConditionSchema = z.object({
  field: z.string().min(1, 'Trường là bắt buộc'),
  operator: z.nativeEnum(ConditionOperator),
  value: z.union([z.string(), z.number(), z.array(z.string()), z.array(z.number())]),
});

/**
 * Schema validation cho nhóm điều kiện segment
 */
export const segmentGroupSchema = z.object({
  conditions: z.array(segmentConditionSchema).min(1, 'Phải có ít nhất 1 điều kiện'),
  logicalOperator: z.enum(['AND', 'OR']).default('AND'),
});

/**
 * Schema validation cho segment
 */
export const segmentSchema = z.object({
  name: z
    .string()
    .min(2, 'Tên phải có ít nhất 2 ký tự')
    .max(100, 'Tên không được vượt quá 100 ký tự'),
  description: z.string().max(500, 'Mô tả không được vượt quá 500 ký tự').optional(),
  status: z.nativeEnum(SegmentStatus).default(SegmentStatus.DRAFT),
  audienceId: z.string().min(1, 'Audience là bắt buộc'),
  groups: z.array(segmentGroupSchema).min(1, 'Phải có ít nhất 1 nhóm điều kiện'),
});

export type SegmentFormValues = z.infer<typeof segmentSchema>;
