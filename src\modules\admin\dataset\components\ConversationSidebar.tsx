import React from 'react';
import { Typography, Tooltip } from '@/shared/components/common';
import { ImportedConversation } from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { MessageCircle, Trash2, Upload, PanelLeftClose, PanelLeft } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { extractTextFromContent } from '../types/jsonl.types';

interface ConversationSidebarProps {
  /**
   * Danh sách conversations
   */
  conversations: ImportedConversation[];

  /**
   * Conversation hiện tại được chọn
   */
  selectedConversationId: string | null;

  /**
   * Callback khi chọn conversation
   */
  onSelectConversation: (conversationId: string) => void;

  /**
   * Callback khi xóa conversation
   */
  onDeleteConversation: (conversationId: string) => void;

  /**
   * Callback khi import file
   */
  onImportFile: (event: React.ChangeEvent<HTMLInputElement>) => void;

  /**
   * Callback khi tạo chat mới
   */
  onNewChat: () => void;

  /**
   * Trạng thái sidebar có mở hay không
   */
  isOpen: boolean;

  /**
   * Callback toggle sidebar
   */
  onToggleSidebar: () => void;
}

/**
 * Component sidebar hiển thị danh sách conversations
 */
const ConversationSidebar: React.FC<ConversationSidebarProps> = ({
  conversations,
  selectedConversationId,
  onSelectConversation,
  onDeleteConversation,
  onImportFile,
  onNewChat,
  isOpen,
  onToggleSidebar,
}) => {
  const { t } = useTranslation();

  // Tạo title từ first user message
  const getConversationTitle = (conversation: ImportedConversation): string => {
    // Nếu đã có title từ conversation, dùng luôn
    if (conversation.title && conversation.title !== 'New Conversation') {
      return conversation.title;
    }

    const firstUserMessage = conversation.messages.find(msg => msg.role === 'user');
    if (firstUserMessage) {
      return typeof firstUserMessage.content === 'string'
        ? firstUserMessage.content.length > 30
          ? firstUserMessage.content.substring(0, 30) + '...'
          : firstUserMessage.content
        : extractTextFromContent(firstUserMessage.content).length > 30
          ? extractTextFromContent(firstUserMessage.content).substring(0, 30) + '...'
          : extractTextFromContent(firstUserMessage.content);
    }
    return conversation.title || `Chat ${conversation.id.substring(0, 6)}`;
  };

  return (
    <div
      className={`h-full bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ${
        isOpen ? 'w-80' : 'w-0'
      } overflow-hidden`}
    >
      {isOpen && (
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <Typography variant="h6" className="flex items-center">
                <MessageCircle size={20} className="mr-2 text-primary" />
                {t('user-dataset:conversationSidebar.title')}
              </Typography>

              {/* Toggle Button */}
              <Tooltip content={t('Đóng sidebar')} position="bottom">
                <button
                  onClick={onToggleSidebar}
                  className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                >
                  <PanelLeftClose size={18} className="text-gray-600 dark:text-gray-400" />
                </button>
              </Tooltip>
            </div>

            {/* Import Button */}
            <label className="w-full btn btn-outline inline-flex items-center justify-center font-medium transition-all duration-200 px-3 py-2 cursor-pointer text-sm mb-2">
              <Upload size={16} className="mr-2" />
              {t('user-dataset:conversationSidebar.buttons.importJsonl')}
              <input type="file" accept=".jsonl,.json" hidden onChange={onImportFile} />
            </label>

            {/* New Chat Button */}
            <button
              onClick={onNewChat}
              className="w-full btn btn-primary inline-flex items-center justify-center font-medium transition-all duration-200 px-3 py-2 text-sm"
            >
              <MessageCircle size={16} className="mr-2" />
              {t('user-dataset:conversationSidebar.buttons.newChat')}
            </button>
          </div>

          {/* Conversations List */}
          <div className="flex-1 min-h-0 overflow-hidden">
            <div className="h-full overflow-y-auto px-2 hide-scrollbar">
              {conversations.length === 0 ? (
                <div className="p-4 text-center">
                  <div className="text-4xl mb-2">💬</div>
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    {t('user-dataset:conversationSidebar.messages.noConversations')}
                  </p>
                  <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
                    {t('user-dataset:conversationSidebar.messages.importToStart')}
                  </p>
                </div>
              ) : (
                <div className="py-2 space-y-1">
                  {conversations.map(conversation => (
                    <div
                      key={conversation.id}
                      className={`group relative p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedConversationId === conversation.id
                          ? 'bg-primary/10 border border-primary/20'
                          : 'hover:bg-gray-50 dark:hover:bg-gray-800 border border-transparent'
                      }`}
                      onClick={() => onSelectConversation(conversation.id)}
                    >
                      {/* Conversation Title */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {getConversationTitle(conversation)}
                          </h4>
                        </div>

                        {/* Delete Button */}
                        <Tooltip
                          content={t('user-dataset:conversationSidebar.buttons.deleteConversation')}
                          position="top"
                        >
                          <button
                            onClick={e => {
                              e.stopPropagation();
                              onDeleteConversation(conversation.id);
                            }}
                            className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-red-100 dark:hover:bg-red-900/20 text-red-500 transition-opacity"
                          >
                            <Trash2 size={14} />
                          </button>
                        </Tooltip>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Toggle Button khi sidebar đóng */}
      {!isOpen && (
        <div className="absolute top-4 left-4 z-10">
          <Tooltip
            content={t('user-dataset:conversationSidebar.buttons.openSidebar')}
            position="right"
          >
            <button
              onClick={onToggleSidebar}
              className="p-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <PanelLeft size={18} className="text-gray-600 dark:text-gray-400" />
            </button>
          </Tooltip>
        </div>
      )}
    </div>
  );
};

export default ConversationSidebar;
