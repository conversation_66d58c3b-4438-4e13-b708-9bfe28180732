
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  MessageCircle,
  Target,
  Users,
  Bell,
  ArrowRight,
  Zap,
  TrendingUp,
  Settings
} from 'lucide-react';
import { Card, Button } from '@/shared/components/common';
import { useZaloAccounts } from '../../hooks/zalo/useZaloAccounts';
import { useZaloAdsAccounts } from '../../hooks/zalo-ads/useZaloAdsAccounts';
import { useZaloAdsMetrics } from '../../hooks/zalo-ads/useZaloAdsMetrics';

/**
 * Trang tổng quan Zalo Ecosystem - tích hợp OA và Ads
 */
export function ZaloEcosystemPage() {
  const { t } = useTranslation('marketing');
  const navigate = useNavigate();

  // API calls
  const { data: oaAccounts } = useZaloAccounts({ page: 1, limit: 10 });
  const { data: adsAccounts } = useZaloAdsAccounts({ page: 1, limit: 10 });
  const { data: adsMetrics } = useZaloAdsMetrics();

  // Mock data for demonstration
  const mockOaStats = {
    totalAccounts: oaAccounts?.meta?.totalItems || 3,
    totalFollowers: 15420,
    messagesSent: 2850,
    engagementRate: 8.5
  };

  const mockAdsStats = {
    totalAccounts: adsAccounts?.meta?.totalItems || 2,
    activeCampaigns: adsMetrics?.activeCampaigns || 8,
    totalSpend: adsMetrics?.totalSpend || ********,
    roas: adsMetrics?.averageRoas || 3.2
  };

  const ecosystemFeatures = [
    {
      id: 'oa',
      title: t('marketing:zalo.ecosystem.oa.title', 'Zalo Official Account'),
      description: t('marketing:zalo.ecosystem.oa.description', 'Quản lý tài khoản OA, followers và tin nhắn'),
      icon: MessageCircle,
      color: 'from-info to-info',
      stats: [
        { label: t('marketing:zalo.ecosystem.oa.accounts', 'Tài khoản OA'), value: mockOaStats.totalAccounts },
        { label: t('marketing:zalo.ecosystem.oa.followers', 'Followers'), value: new Intl.NumberFormat('vi-VN').format(mockOaStats.totalFollowers) },
        { label: t('marketing:zalo.ecosystem.oa.messages', 'Tin nhắn'), value: new Intl.NumberFormat('vi-VN').format(mockOaStats.messagesSent) },
        { label: t('marketing:zalo.ecosystem.oa.engagement', 'Tương tác'), value: `${mockOaStats.engagementRate}%` }
      ],
      actions: [
        { label: t('marketing:zalo.ecosystem.oa.manage', 'Quản lý OA'), path: '/marketing/zalo/accounts' },
        { label: t('marketing:zalo.ecosystem.oa.followers', 'Quản lý Followers'), path: '/marketing/zalo/followers' },
        { label: t('marketing:zalo.ecosystem.oa.zns', 'ZNS Templates'), path: '/marketing/zalo/zns' }
      ]
    },
    {
      id: 'ads',
      title: t('marketing:zalo.ecosystem.ads.title', 'Zalo Ads'),
      description: t('marketing:zalo.ecosystem.ads.description', 'Quảng cáo trả phí để mở rộng tiếp cận'),
      icon: Target,
      color: 'from-primary to-secondary',
      stats: [
        { label: t('marketing:zalo.ecosystem.ads.accounts', 'Tài khoản Ads'), value: mockAdsStats.totalAccounts },
        { label: t('marketing:zalo.ecosystem.ads.campaigns', 'Chiến dịch'), value: mockAdsStats.activeCampaigns },
        { label: t('marketing:zalo.ecosystem.ads.spend', 'Chi phí'), value: new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND', maximumFractionDigits: 0 }).format(mockAdsStats.totalSpend) },
        { label: t('marketing:zalo.ecosystem.ads.roas', 'ROAS'), value: `${mockAdsStats.roas}x` }
      ],
      actions: [
        { label: t('marketing:zalo.ecosystem.ads.accounts', 'Quản lý tài khoản'), path: '/marketing/zalo-ads/accounts' },
        { label: t('marketing:zalo.ecosystem.ads.campaigns', 'Quản lý chiến dịch'), path: '/marketing/zalo-ads/campaigns' },
        { label: t('marketing:zalo.ecosystem.ads.reports', 'Báo cáo'), path: '/marketing/zalo-ads/reports' }
      ]
    }
  ];

  const integrationOpportunities = [
    {
      title: t('marketing:zalo.ecosystem.integration.retargeting.title', 'Retargeting từ OA'),
      description: t('marketing:zalo.ecosystem.integration.retargeting.description', 'Tạo chiến dịch quảng cáo targeting followers của OA'),
      icon: Users,
      action: () => navigate('/marketing/zalo-ads/campaigns?source=oa-followers')
    },
    {
      title: t('marketing:zalo.ecosystem.integration.lookalike.title', 'Lookalike Audience'),
      description: t('marketing:zalo.ecosystem.integration.lookalike.description', 'Tìm khách hàng tương tự như followers hiện tại'),
      icon: TrendingUp,
      action: () => navigate('/marketing/zalo-ads/campaigns?audience=lookalike')
    },
    {
      title: t('marketing:zalo.ecosystem.integration.zns.title', 'ZNS sau Ads'),
      description: t('marketing:zalo.ecosystem.integration.zns.description', 'Gửi ZNS follow-up cho người tương tác với quảng cáo'),
      icon: Bell,
      action: () => navigate('/marketing/zalo/zns?trigger=ads-interaction')
    }
  ];

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Ecosystem Overview */}
      <div className="grid gap-6 lg:grid-cols-2">
        {ecosystemFeatures.map((feature) => (
          <Card key={feature.id} className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={`h-12 w-12 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center text-white`}>
                  <feature.icon className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">{feature.title}</h3>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={() => navigate(feature.actions[0].path)}>
                <Settings className="h-4 w-4" />
              </Button>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              {feature.stats.map((stat, index) => (
                <div key={index} className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className="text-lg font-bold">{stat.value}</div>
                  <div className="text-xs text-muted-foreground">{stat.label}</div>
                </div>
              ))}
            </div>

            {/* Actions */}
            <div className="space-y-2">
              {feature.actions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="w-full justify-between"
                  onClick={() => navigate(action.path)}
                >
                  {action.label}
                  <ArrowRight className="h-4 w-4" />
                </Button>
              ))}
            </div>
          </Card>
        ))}
      </div>

      {/* Integration Opportunities */}
      <Card className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="h-10 w-10 rounded-lg bg-gradient-to-r from-secondary to-primary flex items-center justify-center text-white">
            <Zap className="h-5 w-5" />
          </div>
          <div>
            <h3 className="text-lg font-semibold">
              {t('marketing:zalo.ecosystem.integration.title', 'Cơ hội tích hợp')}
            </h3>
            <p className="text-sm text-muted-foreground">
              {t('marketing:zalo.ecosystem.integration.description', 'Tối ưu hóa hiệu quả bằng cách kết hợp OA và Ads')}
            </p>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          {integrationOpportunities.map((opportunity, index) => (
            <Card key={index} className="p-4 hover:shadow-lg transition-shadow cursor-pointer" onClick={opportunity.action}>
              <div className="flex items-start space-x-3">
                <div className="h-8 w-8 rounded bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white flex-shrink-0">
                  <opportunity.icon className="h-4 w-4" />
                </div>
                <div>
                  <h4 className="font-medium mb-1">{opportunity.title}</h4>
                  <p className="text-sm text-muted-foreground">{opportunity.description}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </Card>

      {/* Performance Comparison */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">
          {t('marketing:zalo.ecosystem.performance.title', 'So sánh hiệu suất')}
        </h3>

        <div className="grid gap-4 md:grid-cols-2">
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium">Zalo OA</h4>
              <MessageCircle className="h-5 w-5 text-info" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{t('marketing:zalo.ecosystem.performance.reach', 'Tiếp cận')}</span>
                <span className="font-medium">{new Intl.NumberFormat('vi-VN').format(mockOaStats.totalFollowers)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>{t('marketing:zalo.ecosystem.performance.engagement', 'Tương tác')}</span>
                <span className="font-medium">{mockOaStats.engagementRate}%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>{t('marketing:zalo.ecosystem.performance.cost', 'Chi phí')}</span>
                <span className="font-medium text-success">{t('marketing:zalo.ecosystem.performance.free', 'Miễn phí')}</span>
              </div>
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium">Zalo Ads</h4>
              <Target className="h-5 w-5 text-warning" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{t('marketing:zalo.ecosystem.performance.reach', 'Tiếp cận')}</span>
                <span className="font-medium">{new Intl.NumberFormat('vi-VN').format(2450000)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>{t('marketing:zalo.ecosystem.performance.conversion', 'Chuyển đổi')}</span>
                <span className="font-medium">0.75%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>{t('marketing:zalo.ecosystem.performance.roas', 'ROAS')}</span>
                <span className="font-medium text-success">{mockAdsStats.roas}x</span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <h5 className="font-medium text-blue-900 mb-2">
            {t('marketing:zalo.ecosystem.performance.recommendation.title', 'Khuyến nghị')}
          </h5>
          <p className="text-sm text-blue-800">
            {t('marketing:zalo.ecosystem.performance.recommendation.description', 'Kết hợp OA để nurture leads và Ads để mở rộng tiếp cận. Sử dụng ZNS để duy trì kết nối với khách hàng.')}
          </p>
        </div>
      </Card>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">
          {t('marketing:zalo.ecosystem.quickActions.title', 'Thao tác nhanh')}
        </h3>

        <div className="grid gap-3 md:grid-cols-4">
          <Button variant="outline" onClick={() => navigate('/marketing/zalo/accounts?action=connect')} className="gap-2">
            <MessageCircle className="h-4 w-4" />
            {t('marketing:zalo.ecosystem.quickActions.connectOA', 'Kết nối OA')}
          </Button>
          <Button variant="outline" onClick={() => navigate('/marketing/zalo-ads/accounts?action=connect')} className="gap-2">
            <Target className="h-4 w-4" />
            {t('marketing:zalo.ecosystem.quickActions.connectAds', 'Kết nối Ads')}
          </Button>
          <Button variant="outline" onClick={() => navigate('/marketing/zalo/zns?action=create')} className="gap-2">
            <Bell className="h-4 w-4" />
            {t('marketing:zalo.ecosystem.quickActions.createZNS', 'Tạo ZNS')}
          </Button>
          <Button variant="outline" onClick={() => navigate('/marketing/zalo-ads/campaigns?action=create')} className="gap-2">
            <Target className="h-4 w-4" />
            {t('marketing:zalo.ecosystem.quickActions.createCampaign', 'Tạo chiến dịch')}
          </Button>
        </div>
      </Card>
    </div>
  );
}

export default ZaloEcosystemPage;
