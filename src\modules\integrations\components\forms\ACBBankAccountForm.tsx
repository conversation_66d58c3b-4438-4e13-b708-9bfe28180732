import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Typography,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { ACBBankAccountFormValues } from '../../types/banking.types';
import { acbBankAccountSchema } from '../../schemas/banking.schema';
import { NotificationUtil } from '@/shared/utils/notification';

interface ACBBankAccountFormProps {
  /**
   * D<PERSON> liệu ban đầu (cho chế độ chỉnh sửa)
   */
  initialData?: ACBBankAccountFormValues;

  /**
   * Callback khi submit form
   */
  onSubmit: (values: ACBBankAccountFormValues) => Promise<void>;

  /**
   * Callback khi hủy
   */
  onCancel?: () => void;

  /**
   * Trạng thái loading
   */
  loading?: boolean;
}

/**
 * Form liên kết tài khoản ngân hàng ACB
 */
const ACBBankAccountForm: React.FC<ACBBankAccountFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
}) => {
  const { t } = useTranslation(['integrations', 'common']);
  const { formRef, setFormErrors } = useFormErrors<ACBBankAccountFormValues>();

  // State cho form data
  const [formData, setFormData] = useState<ACBBankAccountFormValues>({
    account_holder_name: initialData?.account_holder_name || '',
    account_number: initialData?.account_number || '',
    phone_number: initialData?.phone_number || '',
    label: initialData?.label || '',
  });

  // Xử lý thay đổi input
  const handleInputChange = (field: keyof ACBBankAccountFormValues, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Xử lý submit form
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    try {
      // Validate dữ liệu với Zod schema
      const validatedData = acbBankAccountSchema.parse(formData);

      // Prepare data for submission, ensuring optional fields are handled correctly
      const submitData: ACBBankAccountFormValues = {
        account_holder_name: validatedData.account_holder_name,
        account_number: validatedData.account_number,
        phone_number: validatedData.phone_number,
        ...(validatedData.label && validatedData.label.trim() !== '' && { label: validatedData.label }),
      };

      // Gọi callback onSubmit
      await onSubmit(submitData);
    } catch (error) {
      if (error instanceof Error && 'issues' in error) {
        // Zod validation errors
        const zodError = error as any;
        const fieldErrors: Partial<ACBBankAccountFormValues> = {};
        
        zodError.issues.forEach((issue: any) => {
          const fieldName = issue.path[0] as keyof ACBBankAccountFormValues;
          fieldErrors[fieldName] = issue.message;
        });

        setFormErrors(fieldErrors);
      } else {
        console.error('Form submission error:', error);
        NotificationUtil.error({
          message: t('integrations:banking.acb.submitError', 'Có lỗi xảy ra khi lưu thông tin tài khoản'),
          duration: 5000,
        });
      }
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <Card className="border-0">
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <Typography variant="h5" className="font-semibold mb-2">
              {t('integrations:banking.acb.title', 'Liên kết tài khoản ACB')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('integrations:banking.acb.description', 'Nhập thông tin tài khoản ngân hàng ACB để liên kết với hệ thống')}
            </Typography>
          </div>

          {/* Form */}
          <Form ref={formRef as any} onSubmit={handleSubmit as any}>
            <div className="space-y-4">
              {/* Tên chủ tài khoản */}
              <FormItem
                label={t('integrations:banking.acb.accountHolderName', 'Tên chủ tài khoản')}
                name="account_holder_name"
                required
              >
                <Input
                  type="text"
                  value={formData.account_holder_name}
                  onChange={(e) => handleInputChange('account_holder_name', e.target.value)}
                  placeholder={t('integrations:banking.acb.accountHolderNamePlaceholder', 'Nhập tên chủ tài khoản ACB')}
                  fullWidth
                />
              </FormItem>

              {/* Số tài khoản */}
              <FormItem
                label={t('integrations:banking.acb.accountNumber', 'Số tài khoản')}
                name="account_number"
                required
              >
                <Input
                  type="text"
                  value={formData.account_number}
                  onChange={(e) => handleInputChange('account_number', e.target.value)}
                  placeholder={t('integrations:banking.acb.accountNumberPlaceholder', 'Nhập số tài khoản ACB')}
                  maxLength={20}
                  fullWidth
                />
              </FormItem>

              {/* Số điện thoại */}
              <FormItem
                label={t('integrations:banking.acb.phoneNumber', 'Số điện thoại')}
                name="phone_number"
                required
              >
                <Input
                  type="tel"
                  value={formData.phone_number}
                  onChange={(e) => handleInputChange('phone_number', e.target.value)}
                  placeholder={t('integrations:banking.acb.phoneNumberPlaceholder', 'Nhập số điện thoại đăng ký ACB')}
                  maxLength={20}
                  fullWidth
                />
              </FormItem>

              {/* Tên gợi nhớ */}
              <FormItem
                label={t('integrations:banking.acb.label', 'Tên gợi nhớ')}
                name="label"
              >
                <Input
                  type="text"
                  value={formData.label}
                  onChange={(e) => handleInputChange('label', e.target.value)}
                  placeholder={t('integrations:banking.acb.labelPlaceholder', 'Nhập tên gợi nhớ (tùy chọn)')}
                  maxLength={100}
                  fullWidth
                />
              </FormItem>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={loading}
                >
                  {t('common:cancel', 'Hủy')}
                </Button>
              )}
              <Button
                type="submit"
                variant="primary"
                isLoading={loading}
              >
                {initialData 
                  ? t('common:update', 'Cập nhật')
                  : t('common:save', 'Lưu')
                }
              </Button>
            </div>
          </Form>
        </div>
      </Card>
    </div>
  );
};

export default ACBBankAccountForm;
