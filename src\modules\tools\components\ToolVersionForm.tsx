import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Textarea, Select, FormItem } from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import { ToolStatus } from '../types/common.types';
import { EditUserToolVersionParams, UserToolVersion } from '../types/user-tool.types';
import JsonEditor from './JsonEditor';

interface ToolVersionFormProps {
  initialValues?: UserToolVersion;
  onSubmit: (values: EditUserToolVersionParams) => void;
  onCancel: () => void;
  readOnly?: boolean;
  isLoading?: boolean;
  isEdit?: boolean;
}

/**
 * Component form để chỉnh sửa phiên bản tool của user
 */
const ToolVersionForm: React.FC<ToolVersionFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
  readOnly = false,
  isLoading = false,
}) => {
  const { t } = useTranslation(['tools', 'common']);

  // State cho form
  const [toolDescription, setToolDescription] = useState(initialValues?.toolDescription || '');
  const [parameters, setParameters] = useState<Record<string, unknown>>(
    initialValues?.parameters || {}
  );
  const [changeDescription, setChangeDescription] = useState(
    initialValues?.changeDescription || ''
  );
  const [status, setStatus] = useState<ToolStatus>(initialValues?.status || ToolStatus.DRAFT);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Cập nhật state khi có initialValues
  useEffect(() => {
    if (initialValues) {
      setToolDescription(initialValues.toolDescription || '');
      setParameters(initialValues.parameters || {});
      setChangeDescription(initialValues.changeDescription || '');
      setStatus(initialValues.status || ToolStatus.DRAFT);
    }
  }, [initialValues]);

  // Xử lý thay đổi parameters
  const handleParametersChange = (parsedParams: Record<string, unknown>) => {
    setParameters(parsedParams);
    if (errors['parameters']) {
      setErrors(prev => ({ ...prev, parameters: '' }));
    }
  };

  // Xử lý lỗi JSON
  const handleJsonError = (errorMessage: string | null) => {
    if (errorMessage) {
      setErrors(prev => ({
        ...prev,
        parameters: t('tools:form.invalidJson', 'Invalid JSON format'),
      }));
    }
  };

  // Xử lý submit form
  const handleSubmit = () => {
    // Validate form
    const newErrors: Record<string, string> = {};

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Chuẩn bị dữ liệu để submit
    const formData: EditUserToolVersionParams = {
      toolDescription: toolDescription,
      parameters,
      changeDescription: changeDescription,
      status,
    };

    onSubmit(formData);

    // Hiển thị thông báo thành công
    NotificationUtil.success({
      message: t('tools:updateVersionSuccess', 'Phiên bản đã được cập nhật thành công!'),
      duration: 3000,
    });
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <Typography variant="h5">
          {t('tools:editToolVersion', 'Chỉnh sửa phiên bản tool')}
        </Typography>
      </div>

      <div className="space-y-4">
        <FormItem label={t('tools:form.toolDescription', 'Mô tả hiển thị')}>
          <Textarea
            value={toolDescription || ''}
            onChange={e => setToolDescription(e.target.value)}
            placeholder={t('tools:form.toolDescriptionPlaceholder', 'Nhập mô tả hiển thị của tool')}
            disabled={readOnly || isLoading}
            rows={3}
          />
        </FormItem>

        <FormItem
          label={t('tools:form.parameters', 'Tham số')}
          helpText={errors['parameters']}
          required
        >
          <JsonEditor
            value={parameters}
            onChange={handleParametersChange}
            onError={handleJsonError}
            placeholder={t('tools:form.parametersPlaceholder', 'Nhập tham số của tool dạng JSON')}
            disabled={readOnly || isLoading}
            minHeight={200}
            className="font-mono text-sm"
          />
        </FormItem>

        <FormItem label={t('tools:form.changeDescription', 'Mô tả thay đổi')}>
          <Textarea
            value={changeDescription || ''}
            onChange={e => setChangeDescription(e.target.value)}
            placeholder={t(
              'tools:form.changeDescriptionPlaceholder',
              'Mô tả những thay đổi trong phiên bản này'
            )}
            disabled={readOnly || isLoading}
            rows={3}
          />
        </FormItem>

        <FormItem label={t('tools:form.status', 'Trạng thái')}>
          <Select
            value={status}
            onChange={val => setStatus(val as ToolStatus)}
            options={[
              { value: ToolStatus.DRAFT, label: t('tools:status.draft', 'Bản nháp') },
              { value: ToolStatus.APPROVED, label: t('tools:status.approved', 'Đã duyệt') },
              {
                value: ToolStatus.DEPRECATED,
                label: t('tools:status.deprecated', 'Không dùng nữa'),
              },
            ]}
            disabled={readOnly || isLoading}
          />
        </FormItem>
      </div>

      {!readOnly && (
        <div className="flex justify-end items-center pt-4">
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onCancel} disabled={isLoading}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="primary" onClick={handleSubmit} disabled={isLoading}>
              {isLoading ? t('common.processing', 'Đang xử lý...') : t('common.save', 'Lưu')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ToolVersionForm;
