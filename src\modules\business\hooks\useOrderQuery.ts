import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { OrderService, OrderQueryParams, CreateOrderData, UpdateOrderData, CancelOrderData, transformOrderListItem } from '../services/order.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { useTranslation } from 'react-i18next';

/**
 * Query keys cho order API
 */
export const ORDER_QUERY_KEYS = {
  all: ['business', 'orders'] as const,
  list: (params: OrderQueryParams) => [...ORDER_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...ORDER_QUERY_KEYS.all, 'detail', id] as const,
  statusStats: () => [...ORDER_QUERY_KEYS.all, 'status-stats'] as const,
  track: (id: number) => [...ORDER_QUERY_KEYS.all, 'track', id] as const,
};

/**
 * Hook lấy danh sách đơn hàng
 */
export const useOrders = (params: OrderQueryParams = {}) => {

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.list(params),
    queryFn: () => {
      console.log('🔍 [useOrders] Fetching orders with params:', params);
      return OrderService.getOrders(params);
    },
    select: (data) => {
      // Check if data structure is correct
      if (!data?.result?.items) {
        console.warn('⚠️ [useOrders] Invalid data structure:', data);
        return {
          items: [],
          meta: {
            currentPage: 1,
            totalItems: 0,
            totalPages: 0,
            itemsPerPage: 10,
            itemCount: 0,
          }
        };
      }

      // Transform dữ liệu từ API thành format hiển thị
      const transformedItems = data.result.items.map(transformOrderListItem);
      return {
        ...data.result,
        items: transformedItems,
      };
    },
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook để lấy chi tiết đơn hàng
 */
export const useOrder = (orderId: number) => {

  const query = useQuery({
    queryKey: ORDER_QUERY_KEYS.detail(orderId),
    queryFn: () => {
      console.log('🔍 [useOrder] Fetching order detail for ID:', orderId);
      return OrderService.getOrderById(orderId);
    },
    enabled: !!orderId && orderId > 0,
    refetchOnWindowFocus: false,
    staleTime: 300000, // 5 minutes
  });

  return query;
};

/**
 * Hook tạo đơn hàng mới
 */
export const useCreateOrder = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (data: CreateOrderData) => OrderService.createOrder(data),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:order.createSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.all,
        exact: false,
      });
    },
  });
};

/**
 * Hook cập nhật đơn hàng
 */
export const useUpdateOrder = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateOrderData }) =>
      OrderService.updateOrder(id, data),
    onSuccess: (_, variables) => {
      NotificationUtil.success({
        message: t('business:order.updateSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.detail(variables.id),
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.all,
        exact: false,
      });
    },
  });
};

/**
 * Hook xóa đơn hàng
 */
export const useDeleteOrder = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (id: number) => OrderService.deleteOrder(id),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:order.deleteSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.all,
        exact: false,
      });
    },
  });
};

/**
 * Hook xóa nhiều đơn hàng
 */
export const useBulkDeleteOrders = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (orderIds: number[]) => OrderService.deleteBulkOrders(orderIds),
    onSuccess: (_, orderIds) => {
      NotificationUtil.success({
        message: t('business:order.bulkDeleteSuccess', { count: orderIds.length }) ||
                `Xóa thành công ${orderIds.length} đơn hàng`,
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: () => {
      NotificationUtil.error({
        message: t('business:order.bulkDeleteError') || 'Có lỗi xảy ra khi xóa đơn hàng',
        duration: 3000,
      });
    },
  });
};

/**
 * Hook lấy danh sách đơn hàng của khách hàng cụ thể
 */
export const useCustomerOrders = (userConvertCustomerId: string, params: Omit<OrderQueryParams, 'userConvertCustomerId'> = {}) => {

  const queryParams = {
    ...params,
    userConvertCustomerId,
  };

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.list(queryParams),
    queryFn: () => OrderService.getOrders(queryParams),
    select: (data) => {
      // Trả về dữ liệu thô từ API cho customer orders
      return data.result;
    },
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
    enabled: !!userConvertCustomerId, // Chỉ gọi API khi có userConvertCustomerId
  });
};



/**
 * Hook lấy thống kê trạng thái đơn hàng
 */
export const useOrderStatusStats = () => {

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.statusStats(),
    queryFn: () => OrderService.getOrderStatusStats(),
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 300000, // 5 minutes
  });
};

/**
 * Hook tracking đơn hàng
 */
export const useOrderTracking = (orderId: number) => {

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.track(orderId),
    queryFn: () => OrderService.trackOrder(orderId),
    select: (data) => data.result,
    enabled: !!orderId,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 1 minute
  });
};

/**
 * Hook hủy đơn hàng
 */
export const useCancelOrder = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (data: CancelOrderData) => {
      return OrderService.cancelOrder(data);
    },
    onSuccess: (response, variables) => {
      NotificationUtil.success({
        message: response.message || t('business:order.cancelSuccess', 'Hủy đơn hàng thành công'),
        duration: 3000,
      });

      // Invalidate và refetch order detail
      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.detail(variables.orderId),
      });

      // Invalidate và refetch order list
      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.all,
        exact: false,
      });

      // Invalidate status stats để cập nhật thống kê
      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.statusStats(),
      });
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message ||
                          error?.message ||
                          t('business:order.cancelError', 'Có lỗi xảy ra khi hủy đơn hàng');

      NotificationUtil.error({
        message: errorMessage,
        duration: 5000,
      });
    },
  });
};
