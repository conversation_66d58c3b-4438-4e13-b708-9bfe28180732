import { Suspense, lazy } from 'react';
import MainLayout from '@/shared/layouts/MainLayout';
import { Loading } from '@/shared/components/common';
import i18n from '@/lib/i18n';
import { RouteObject } from 'react-router-dom';

// Lazy load pages
const BusinessPage = lazy(() => import('@/modules/business/pages/BusinessPage'));
const ProductsPage = lazy(() => import('@/modules/business/pages/ProductsPage'));
const ConversionPage = lazy(() => import('@/modules/business/pages/ConversionPage'));
const ConversionDetailPage = lazy(() => import('@/modules/business/pages/ConversionDetailPage'));
const OrderPage = lazy(() => import('@/modules/business/pages/OrderPage'));
const OrderDetailPage = lazy(() => import('@/modules/business/pages/OrderDetailPage'));
const CustomerPage = lazy(() => import('@/modules/business/pages/CustomerPage'));
const ReportPage = lazy(() => import('@/modules/business/pages/ReportPage'));
const InventoryPage = lazy(() => import('@/modules/business/pages/InventoryPage'));
const WarehouseDetailPage = lazy(() => import('@/modules/business/pages/WarehouseDetailPage'));
const PhysicalWarehouseDetailPage = lazy(() => import('@/modules/business/pages/PhysicalWarehouseDetailPage'));
const CustomFieldPage = lazy(() => import('@/modules/business/pages/CustomFieldPage'));
const CreateProductPage = lazy(() => import('@/modules/business/pages/CreateProductPage'));
const VirtualWarehousePage = lazy(() => import('@/modules/business/pages/VirtualWarehousePage'));
const VirtualWarehouseDetailPage = lazy(() => import('@/modules/business/components/virtual-warehouse/VirtualWarehouseDetailPage'));
const ShopPage = lazy(() => import('@/modules/business/pages/ShopPage'));
const CompanyCreatePage = lazy(() => import('@/modules/business/pages/CompanyCreatePage'));
const CompanyDemoPage = lazy(() => import('@/modules/business/pages/CompanyDemoPage'));

/**
 * Business module routes
 */
export const businessRoutes: RouteObject[] = [
  // Trang tổng quan
  {
    path: '/business',
    element: (
      <MainLayout title={i18n.t('business:title', 'Quản lý kinh doanh')}>
        <Suspense fallback={<Loading />}>
          <BusinessPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang sản phẩm
  {
    path: '/business/product',
    element: (
      <MainLayout title={i18n.t('business:product.title', 'Sản phẩm')}>
        <Suspense fallback={<Loading />}>
          <ProductsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang chuyển đổi
  {
    path: '/business/conversion',
    element: (
      <MainLayout title={i18n.t('business:conversion.title', 'Chuyển đổi')}>
        <Suspense fallback={<Loading />}>
          <ConversionPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang chi tiết chuyển đổi
  {
    path: '/business/conversion/:id',
    element: (
      <MainLayout title={i18n.t('business:conversion.detail.title', 'Chi tiết chuyển đổi')}>
        <Suspense fallback={<Loading />}>
          <ConversionDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang đơn hàng
  {
    path: '/business/order',
    element: (
      <MainLayout title={i18n.t('business:order.title', 'Đơn hàng')}>
        <Suspense fallback={<Loading />}>
          <OrderPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang chi tiết đơn hàng
  {
    path: '/business/order/:id',
    element: (
      <MainLayout title={i18n.t('business:order.detail.title', 'Chi tiết đơn hàng')}>
        <Suspense fallback={<Loading />}>
          <OrderDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang khách hàng
  {
    path: '/business/customer',
    element: (
      <MainLayout title={i18n.t('business:customer.title', 'Khách hàng')}>
        <Suspense fallback={<Loading />}>
          <CustomerPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang báo cáo
  {
    path: '/business/report',
    element: (
      <MainLayout title={i18n.t('business:report.title', 'Báo cáo')}>
        <Suspense fallback={<Loading />}>
          <ReportPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang quản lý kho
  {
    path: '/business/inventory',
    element: (
      <MainLayout title={i18n.t('business:inventory.title', 'Quản lý kho')}>
        <Suspense fallback={<Loading />}>
          <InventoryPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang chi tiết kho vật lý (sử dụng usePhysicalWarehouse hook)
  {
    path: '/business/inventory/:warehouseId',
    element: (
      <MainLayout title={i18n.t('business:physicalWarehouse.detailTitle', 'Chi tiết kho vật lý')}>
        <Suspense fallback={<Loading />}>
          <PhysicalWarehouseDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang chi tiết kho (sử dụng useWarehouse hook - cho inventory)
  {
    path: '/business/warehouse/:warehouseId',
    element: (
      <MainLayout title={i18n.t('business:warehouse.detailTitle', 'Chi tiết kho')}>
        <Suspense fallback={<Loading />}>
          <WarehouseDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang trường tùy chỉnh
  {
    path: '/business/custom-field',
    element: (
      <MainLayout title={i18n.t('business:customField.title', 'Trường tùy chỉnh')}>
        <Suspense fallback={<Loading />}>
          <CustomFieldPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/business/products/create',
    element: (
      <MainLayout title={i18n.t('business:product.form.createTitle', 'Tạo sản phẩm')}>
        <Suspense fallback={<Loading />}>
          <CreateProductPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang danh sách kho ảo
  {
    path: '/business/virtual-warehouse',
    element: (
      <MainLayout title={i18n.t('business:virtualWarehouse.title', 'Quản lý kho ảo')}>
        <Suspense fallback={<Loading />}>
          <VirtualWarehousePage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang chi tiết kho ảo
  {
    path: '/business/warehouse/virtual/:warehouseId',
    element: (
      <MainLayout title={i18n.t('business:warehouse.virtual.detailTitle', 'Chi tiết kho ảo')}>
        <Suspense fallback={<Loading />}>
          <VirtualWarehouseDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang quản lý cửa hàng
  {
    path: '/business/shop',
    element: (
      <MainLayout title={i18n.t('business:shop.title', 'Quản lý cửa hàng')}>
        <Suspense fallback={<Loading />}>
          <ShopPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang tạo công ty
  {
    path: '/business/company/create',
    element: (
      <MainLayout title={i18n.t('business:company.createTitle', 'Tạo công ty mới')}>
        <Suspense fallback={<Loading />}>
          <CompanyCreatePage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang demo form tạo công ty
  {
    path: '/business/company/demo',
    element: (
      <MainLayout title="Demo Form Tạo Công ty">
        <Suspense fallback={<Loading />}>
          <CompanyDemoPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default businessRoutes;
