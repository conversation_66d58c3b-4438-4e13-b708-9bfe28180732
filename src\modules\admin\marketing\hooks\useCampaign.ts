import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { CampaignService } from '../services/campaign.service';
import {
  Campaign,
  CampaignStatus,
  CampaignType,
  CreateCampaignRequest,
  UpdateCampaignRequest,
} from '../types/campaign.types';

/**
 * Interface cho tham số filter của campaign hook
 */
interface CampaignFilterParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: CampaignStatus | string;
  type?: CampaignType | string;
  segmentId?: string;
  audienceId?: string;
  [key: string]: unknown;
}

// Key cho React Query
const CAMPAIGN_QUERY_KEY = 'campaigns';

/**
 * Hook để lấy danh sách campaign
 */
export const useCampaigns = (params?: CampaignFilterParams) => {
  return useQuery({
    queryKey: [CAMPAIGN_QUERY_KEY, params],
    queryFn: () => {
      // Chuyển đổi params từ CampaignFilterParams sang CampaignQueryParams
      const queryParams = params
        ? {
            ...params,
            status: params.status as CampaignStatus | undefined,
            type: params.type as CampaignType | undefined,
            segmentId: params.segmentId ? Number(params.segmentId) : undefined,
            audienceId: params.audienceId ? Number(params.audienceId) : undefined,
          }
        : undefined;

      return CampaignService.getCampaigns(queryParams);
    },
    select: data => data.result,
  });
};

/**
 * Hook để lấy chi tiết campaign
 */
export const useCampaign = (id: string) => {
  return useQuery({
    queryKey: [CAMPAIGN_QUERY_KEY, id],
    queryFn: () => CampaignService.getCampaignById(Number(id)),
    enabled: !!id,
  });
};

/**
 * Hook để tạo campaign mới
 */
export const useCreateCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (
      data: Omit<
        Campaign,
        | 'id'
        | 'createdAt'
        | 'updatedAt'
        | 'totalContacts'
        | 'segmentName'
        | 'audienceName'
        | 'audienceId'
        | 'metrics'
      >
    ) => {
      // Chuyển đổi dữ liệu để phù hợp với CreateCampaignRequest
      const createData: CreateCampaignRequest = {
        name: data.name,
        description: data.description,
        type: data.type,
        segmentId: Number(data.segmentId),
        startDate: data.startDate,
        endDate: data.endDate === null ? undefined : data.endDate,
        status: data.status,
      };

      return CampaignService.createCampaign(createData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CAMPAIGN_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật campaign
 */
export const useUpdateCampaign = (id: string) => {
  const queryClient = useQueryClient();
  const numericId = Number(id);

  return useMutation({
    mutationFn: (data: Partial<Campaign>) => {
      // Chuyển đổi dữ liệu để phù hợp với UpdateCampaignRequest
      const updateData: UpdateCampaignRequest = {
        name: data.name,
        description: data.description,
        type: data.type,
        segmentId: data.segmentId !== undefined ? Number(data.segmentId) : undefined,
        startDate: data.startDate,
        endDate: data.endDate === null ? undefined : data.endDate,
        status: data.status,
      };

      return CampaignService.updateCampaign(numericId, updateData);
    },
    onSuccess: updatedCampaign => {
      queryClient.setQueryData([CAMPAIGN_QUERY_KEY, id], updatedCampaign);
      queryClient.invalidateQueries({ queryKey: [CAMPAIGN_QUERY_KEY] });
    },
  });
};

/**
 * Hook để xóa campaign
 */
export const useDeleteCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => CampaignService.deleteCampaign(Number(id)),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: [CAMPAIGN_QUERY_KEY, id] });
      queryClient.invalidateQueries({ queryKey: [CAMPAIGN_QUERY_KEY] });
    },
  });
};
