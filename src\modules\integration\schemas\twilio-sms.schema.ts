/**
 * Twilio SMS Integration Schema
 */

import { z } from 'zod';

/**
 * Schema cho thông tin cấu hình Twilio SMS
 */
export const twilioSmsInfoSchema = z.object({
  TWILIO_AUTH_TOKEN: z.string()
    .min(1, 'Twi<PERSON> Auth Token không được để trống')
    .min(10, 'Twilio Auth Token phải có ít nhất 10 ký tự'),

  TWILIO_BASE_DOMAIN: z.string()
    .min(1, 'Twilio Base Domain không được để trống')
    .min(3, 'Twilio Base Domain phải có ít nhất 3 ký tự'),
});

/**
 * Schema cho form tạo tích hợp Twilio SMS
 */
export const createTwilioSmsIntegrationSchema = z.object({
  integrationName: z.string()
    .min(1, 'Tên tích hợp không được để trống')
    .max(100, 'Tên tích hợp không được quá 100 ký tự'),

  info: twilioSmsInfoSchema,
});

/**
 * Type cho form data
 */
export type CreateTwilioSmsIntegrationFormData = z.infer<typeof createTwilioSmsIntegrationSchema>;
