import React, { useState, forwardRef } from 'react';
import { IconCard } from '@/shared/components/common';
import Calendar from '../DatePicker/Calendar';
import { addMonths, format } from 'date-fns';
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useClick,
  useDismiss,
  useRole,
  useInteractions,
  FloatingPortal,
  FloatingFocusManager,
} from '@floating-ui/react';
import './DoubleDatePicker.css';

export interface DoubleDatePickerProps {
  /**
   * Giá trị đã chọn [startDate, endDate]
   */
  value?: [Date | null, Date | null];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (dates: [Date | null, Date | null]) => void;

  /**
   * Icon hiển thị để mở dropdown
   */
  triggerIcon?: React.ReactNode;

  /**
   * Kích thước của icon trigger
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Disabled component
   */
  disabled?: boolean;

  /**
   * Custom className
   */
  className?: string;

  /**
   * Ẩn border xung quanh icon
   */
  noBorder?: boolean;

  /**
   * Placeholder text
   */
  placeholder?: string;
}



/**
 * DoubleDatePicker component hiển thị 2 tháng calendar liền nhau khi bấm vào icon
 *
 * @example
 * ```tsx
 * import { DoubleDatePicker } from '@/shared/components/common';
 * import { useState } from 'react';
 *
 * const MyComponent = () => {
 *   const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
 *
 *   return (
 *     <DoubleDatePicker
 *       value={dateRange}
 *       onChange={setDateRange}
 *       placeholder="Chọn khoảng thời gian"
 *     />
 *   );
 * };
 * ```
 */
const DoubleDatePicker = forwardRef<HTMLDivElement, DoubleDatePickerProps>(
  (
    {
      value = [null, null],
      onChange,
      triggerIcon,
      size = 'md',
      disabled = false,
      className = '',
      _noBorder = true,
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false);
    const [currentMonth, setCurrentMonth] = useState(new Date());
    const [startDate, endDate] = value;

    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: setIsOpen,
      middleware: [
        offset(8),
        flip({
          fallbackAxisSideDirection: 'start',
        }),
        shift({ padding: 8 }),
      ],
      whileElementsMounted: autoUpdate,
      placement: 'bottom-start',
    });

    const click = useClick(context);
    const dismiss = useDismiss(context);
    const role = useRole(context);

    const { getReferenceProps, getFloatingProps } = useInteractions([
      click,
      dismiss,
      role,
    ]);

    // Handle range selection
    const handleRangeSelect = (start: Date | null, end: Date | null) => {
      onChange?.([start, end]);
    };

    // Get next month for second calendar
    const nextMonth = addMonths(currentMonth, 1);

    // Icon size mapping
    const iconSizeMap = {
      sm: 'sm' as const,
      md: 'md' as const,
      lg: 'lg' as const,
    };



    return (
      <div ref={ref} className="relative">
        {/* Trigger IconCard */}
        <div
          ref={refs.setReference}
          {...getReferenceProps()}
        >
          {triggerIcon ? (
            <div className={className}>{triggerIcon}</div>
          ) : (
            <IconCard
              icon="calendar"
              size={iconSizeMap[size]}
              variant="ghost"
              disabled={disabled}
              className={className}
              title="Chọn khoảng thời gian"
            />
          )}
        </div>

        {/* Dropdown Content */}
        {isOpen && (
          <FloatingPortal>
            <FloatingFocusManager context={context} modal={false}>
              <div
                ref={refs.setFloating}
                style={floatingStyles}
                className="z-[9200] datepicker-dropdown rounded-lg overflow-hidden"
                {...getFloatingProps()}
              >
                <div
                  className="double-datepicker-container bg-card"
                >
                  {/* Left Calendar */}
                  <div className="double-datepicker-left">
                    <Calendar
                      month={currentMonth}
                      onMonthChange={setCurrentMonth}
                      rangeMode={true}
                      startDate={startDate}
                      endDate={endDate}
                      onRangeSelect={handleRangeSelect}
                      showToday={true}
                      firstDayOfWeek={1}
                    />
                  </div>

                  {/* Right Calendar */}
                  <div className="double-datepicker-right">
                    <Calendar
                      month={nextMonth}
                      onMonthChange={(date) => setCurrentMonth(addMonths(date, -1))}
                      rangeMode={true}
                      startDate={startDate}
                      endDate={endDate}
                      onRangeSelect={handleRangeSelect}
                      showToday={true}
                      firstDayOfWeek={1}
                    />
                  </div>
                </div>

                {/* Selected Range Display */}
                {(startDate || endDate) && (
                  <div className="border-t border-border p-4 bg-muted/50">
                    <div className="text-sm text-muted-foreground">
                      {startDate && endDate ? (
                        <>
                          <span className="font-medium">Đã chọn:</span>{' '}
                          {format(startDate, 'dd/MM/yyyy')} - {format(endDate, 'dd/MM/yyyy')}
                        </>
                      ) : startDate ? (
                        <>
                          <span className="font-medium">Từ ngày:</span>{' '}
                          {format(startDate, 'dd/MM/yyyy')} (chọn ngày kết thúc)
                        </>
                      ) : null}
                    </div>
                  </div>
                )}
              </div>
            </FloatingFocusManager>
          </FloatingPortal>
        )}
      </div>
    );
  }
);

DoubleDatePicker.displayName = 'DoubleDatePicker';

export default DoubleDatePicker;
