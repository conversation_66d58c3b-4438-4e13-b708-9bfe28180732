/**
 * Types for tag API
 */

import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Tag status enum
 */
export enum TagStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Tag entity
 */
export interface Tag {
  id: number;
  name: string;
  description?: string;
  status: TagStatus;
  color?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Create tag request
 */
export interface CreateTagRequest {
  name: string;
  description?: string;
  color?: string;
  status?: TagStatus;
}

/**
 * Update tag request
 */
export interface UpdateTagRequest {
  name?: string;
  description?: string;
  color?: string;
  status?: TagStatus;
}

/**
 * Tag response
 */
export type TagResponse = Tag;

/**
 * Tag list response
 */
export type TagListResponse = ApiResponseDto<PaginatedResult<TagResponse>>;

/**
 * Tag paginated response
 */
export type TagPaginatedResponse = ApiResponseDto<PaginatedResult<TagResponse>>;

/**
 * Tag detail response
 */
export type TagDetailResponse = ApiResponseDto<TagResponse>;

/**
 * Tag query params
 */
export interface TagQueryParams {
  search?: string;
  status?: TagStatus;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: string;
}
