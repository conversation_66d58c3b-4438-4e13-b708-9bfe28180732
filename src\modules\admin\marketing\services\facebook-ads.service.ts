import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import { facebookSDK, FacebookAPIResponse } from './facebook-sdk.service';
import {
  FacebookAdsAccountDto,
  FacebookAdsCampaignDto,
  FacebookAdsAdSetDto,
  FacebookAdsAdDto,
  FacebookAdsInsightDto,
  FacebookAdsAccountQueryDto,
  FacebookAdsCampaignQueryDto,
  FacebookAdsInsightQueryDto,
  CreateFacebookAdsAccountDto,
  UpdateFacebookAdsAccountDto,
  CreateFacebookAdsCampaignDto,
  UpdateFacebookAdsCampaignDto,
} from '../types/facebook-ads.types';

/**
 * Facebook Ads API Service
 * Các API functions cho Facebook Ads marketing
 */

// ==================== ACCOUNTS ====================

/**
 * L<PERSON>y danh sách tài khoản Facebook Ads
 */
export const getFacebookAdsAccounts = async (
  params?: FacebookAdsAccountQueryDto
): Promise<ApiResponseDto<PaginatedResult<FacebookAdsAccountDto>>> => {
  return apiClient.get('/marketing/facebook-ads/accounts', { params });
};

/**
 * Lấy chi tiết tài khoản Facebook Ads
 */
export const getFacebookAdsAccount = async (
  accountId: string
): Promise<ApiResponseDto<FacebookAdsAccountDto>> => {
  return apiClient.get(`/marketing/facebook-ads/accounts/${accountId}`);
};

/**
 * Tạo kết nối tài khoản Facebook Ads mới
 */
export const createFacebookAdsAccount = async (
  data: CreateFacebookAdsAccountDto
): Promise<ApiResponseDto<FacebookAdsAccountDto>> => {
  return apiClient.post('/marketing/facebook-ads/accounts', data);
};

/**
 * Cập nhật tài khoản Facebook Ads
 */
export const updateFacebookAdsAccount = async (
  accountId: string,
  data: UpdateFacebookAdsAccountDto
): Promise<ApiResponseDto<FacebookAdsAccountDto>> => {
  return apiClient.put(`/marketing/facebook-ads/accounts/${accountId}`, data);
};

/**
 * Xóa tài khoản Facebook Ads
 */
export const deleteFacebookAdsAccount = async (
  accountId: string
): Promise<ApiResponseDto<void>> => {
  return apiClient.delete(`/marketing/facebook-ads/accounts/${accountId}`);
};

/**
 * Đồng bộ dữ liệu tài khoản Facebook Ads
 */
export const syncFacebookAdsAccount = async (
  accountId: string
): Promise<ApiResponseDto<FacebookAdsAccountDto>> => {
  return apiClient.post(`/marketing/facebook-ads/accounts/${accountId}/sync`);
};

// ==================== CAMPAIGNS ====================

/**
 * Lấy danh sách chiến dịch Facebook Ads
 */
export const getFacebookAdsCampaigns = async (
  params?: FacebookAdsCampaignQueryDto
): Promise<ApiResponseDto<PaginatedResult<FacebookAdsCampaignDto>>> => {
  return apiClient.get('/marketing/facebook-ads/campaigns', { params });
};

/**
 * Lấy chi tiết chiến dịch Facebook Ads
 */
export const getFacebookAdsCampaign = async (
  campaignId: string
): Promise<ApiResponseDto<FacebookAdsCampaignDto>> => {
  return apiClient.get(`/marketing/facebook-ads/campaigns/${campaignId}`);
};

/**
 * Tạo chiến dịch Facebook Ads mới
 */
export const createFacebookAdsCampaign = async (
  data: CreateFacebookAdsCampaignDto
): Promise<ApiResponseDto<FacebookAdsCampaignDto>> => {
  return apiClient.post('/marketing/facebook-ads/campaigns', data);
};

/**
 * Cập nhật chiến dịch Facebook Ads
 */
export const updateFacebookAdsCampaign = async (
  campaignId: string,
  data: UpdateFacebookAdsCampaignDto
): Promise<ApiResponseDto<FacebookAdsCampaignDto>> => {
  return apiClient.put(`/marketing/facebook-ads/campaigns/${campaignId}`, data);
};

/**
 * Xóa chiến dịch Facebook Ads
 */
export const deleteFacebookAdsCampaign = async (
  campaignId: string
): Promise<ApiResponseDto<void>> => {
  return apiClient.delete(`/marketing/facebook-ads/campaigns/${campaignId}`);
};

/**
 * Tạm dừng/Kích hoạt chiến dịch Facebook Ads
 */
export const toggleFacebookAdsCampaign = async (
  campaignId: string,
  status: 'ACTIVE' | 'PAUSED'
): Promise<ApiResponseDto<FacebookAdsCampaignDto>> => {
  return apiClient.patch(`/marketing/facebook-ads/campaigns/${campaignId}/status`, { status });
};

// ==================== AD SETS ====================

/**
 * Lấy danh sách ad sets của chiến dịch
 */
export const getFacebookAdsAdSets = async (
  campaignId: string
): Promise<ApiResponseDto<FacebookAdsAdSetDto[]>> => {
  return apiClient.get(`/marketing/facebook-ads/campaigns/${campaignId}/adsets`);
};

/**
 * Lấy chi tiết ad set
 */
export const getFacebookAdsAdSet = async (
  adSetId: string
): Promise<ApiResponseDto<FacebookAdsAdSetDto>> => {
  return apiClient.get(`/marketing/facebook-ads/adsets/${adSetId}`);
};

/**
 * Tạo ad set mới
 */
export const createFacebookAdsAdSet = async (
  campaignId: string,
  data: unknown // TODO: Define CreateFacebookAdsAdSetDto
): Promise<ApiResponseDto<FacebookAdsAdSetDto>> => {
  return apiClient.post(`/marketing/facebook-ads/campaigns/${campaignId}/adsets`, data);
};

/**
 * Cập nhật ad set
 */
export const updateFacebookAdsAdSet = async (
  adSetId: string,
  data: unknown // TODO: Define UpdateFacebookAdsAdSetDto
): Promise<ApiResponseDto<FacebookAdsAdSetDto>> => {
  return apiClient.put(`/marketing/facebook-ads/adsets/${adSetId}`, data);
};

/**
 * Xóa ad set
 */
export const deleteFacebookAdsAdSet = async (
  adSetId: string
): Promise<ApiResponseDto<void>> => {
  return apiClient.delete(`/marketing/facebook-ads/adsets/${adSetId}`);
};

// ==================== ADS ====================

/**
 * Lấy danh sách ads của ad set
 */
export const getFacebookAdsAds = async (
  adSetId: string
): Promise<ApiResponseDto<FacebookAdsAdDto[]>> => {
  return apiClient.get(`/marketing/facebook-ads/adsets/${adSetId}/ads`);
};

/**
 * Lấy chi tiết ad
 */
export const getFacebookAdsAd = async (
  adId: string
): Promise<ApiResponseDto<FacebookAdsAdDto>> => {
  return apiClient.get(`/marketing/facebook-ads/ads/${adId}`);
};

/**
 * Tạo ad mới
 */
export const createFacebookAdsAd = async (
  adSetId: string,
  data: unknown // TODO: Define CreateFacebookAdsAdDto
): Promise<ApiResponseDto<FacebookAdsAdDto>> => {
  return apiClient.post(`/marketing/facebook-ads/adsets/${adSetId}/ads`, data);
};

/**
 * Cập nhật ad
 */
export const updateFacebookAdsAd = async (
  adId: string,
  data: unknown // TODO: Define UpdateFacebookAdsAdDto
): Promise<ApiResponseDto<FacebookAdsAdDto>> => {
  return apiClient.put(`/marketing/facebook-ads/ads/${adId}`, data);
};

/**
 * Xóa ad
 */
export const deleteFacebookAdsAd = async (
  adId: string
): Promise<ApiResponseDto<void>> => {
  return apiClient.delete(`/marketing/facebook-ads/ads/${adId}`);
};

// ==================== INSIGHTS & ANALYTICS ====================

/**
 * Lấy insights của tài khoản
 */
export const getFacebookAdsAccountInsights = async (
  accountId: string,
  params?: FacebookAdsInsightQueryDto
): Promise<ApiResponseDto<FacebookAdsInsightDto[]>> => {
  return apiClient.get(`/marketing/facebook-ads/accounts/${accountId}/insights`, { params });
};

/**
 * Lấy insights của chiến dịch
 */
export const getFacebookAdsCampaignInsights = async (
  campaignId: string,
  params?: FacebookAdsInsightQueryDto
): Promise<ApiResponseDto<FacebookAdsInsightDto[]>> => {
  return apiClient.get(`/marketing/facebook-ads/campaigns/${campaignId}/insights`, { params });
};

/**
 * Lấy insights của ad set
 */
export const getFacebookAdsAdSetInsights = async (
  adSetId: string,
  params?: FacebookAdsInsightQueryDto
): Promise<ApiResponseDto<FacebookAdsInsightDto[]>> => {
  return apiClient.get(`/marketing/facebook-ads/adsets/${adSetId}/insights`, { params });
};

/**
 * Lấy insights của ad
 */
export const getFacebookAdsAdInsights = async (
  adId: string,
  params?: FacebookAdsInsightQueryDto
): Promise<ApiResponseDto<FacebookAdsInsightDto[]>> => {
  return apiClient.get(`/marketing/facebook-ads/ads/${adId}/insights`, { params });
};

// ==================== AUDIENCES ====================

/**
 * Lấy danh sách Custom Audiences
 */
export const getFacebookAdsCustomAudiences = async (): Promise<ApiResponseDto<unknown[]>> => {
  return apiClient.get('/marketing/facebook-ads/audiences/custom');
};

/**
 * Tạo Custom Audience mới
 */
export const createFacebookAdsCustomAudience = async (
  data: unknown // TODO: Define CreateCustomAudienceDto
): Promise<ApiResponseDto<unknown>> => {
  return apiClient.post('/marketing/facebook-ads/audiences/custom', data);
};

/**
 * Lấy danh sách Lookalike Audiences
 */
export const getFacebookAdsLookalikeAudiences = async (): Promise<ApiResponseDto<unknown[]>> => {
  return apiClient.get('/marketing/facebook-ads/audiences/lookalike');
};

/**
 * Tạo Lookalike Audience mới
 */
export const createFacebookAdsLookalikeAudience = async (
  data: unknown // TODO: Define CreateLookalikeAudienceDto
): Promise<ApiResponseDto<unknown>> => {
  return apiClient.post('/marketing/facebook-ads/audiences/lookalike', data);
};

// ==================== CREATIVES ====================

/**
 * Lấy danh sách creatives
 */
export const getFacebookAdsCreatives = async (): Promise<ApiResponseDto<unknown[]>> => {
  return apiClient.get('/marketing/facebook-ads/creatives');
};

/**
 * Upload creative mới
 */
export const uploadFacebookAdsCreative = async (
  file: File,
  data: unknown // TODO: Define UploadCreativeDto
): Promise<ApiResponseDto<unknown>> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('data', JSON.stringify(data));
  
  return apiClient.post('/marketing/facebook-ads/creatives/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * Xóa creative
 */
export const deleteFacebookAdsCreative = async (
  creativeId: string
): Promise<ApiResponseDto<void>> => {
  return apiClient.delete(`/marketing/facebook-ads/creatives/${creativeId}`);
};

// ==================== DIRECT FACEBOOK SDK FUNCTIONS ====================

/**
 * Lấy thông tin ad account trực tiếp từ Facebook
 */
export const getFacebookAdAccountDirect = async (
  accountId: string
): Promise<FacebookAPIResponse> => {
  return facebookSDK.getAdAccount(accountId);
};

/**
 * Lấy campaigns trực tiếp từ Facebook
 */
export const getFacebookCampaignsDirect = async (
  accountId: string,
  params?: {
    limit?: number;
    after?: string;
    before?: string;
    fields?: string;
  }
): Promise<FacebookAPIResponse> => {
  // Convert params to Record<string, string> format
  const stringParams: Record<string, string> | undefined = params ? {
    ...(params.limit !== undefined && { limit: params.limit.toString() }),
    ...(params.after && { after: params.after }),
    ...(params.before && { before: params.before }),
    ...(params.fields && { fields: params.fields }),
  } : undefined;

  return facebookSDK.getCampaigns(accountId, stringParams);
};

/**
 * Lấy insights của campaign trực tiếp từ Facebook
 */
export const getFacebookCampaignInsightsDirect = async (
  campaignId: string,
  params?: {
    date_preset?: string;
    time_range?: {
      since: string;
      until: string;
    };
    fields?: string;
  }
): Promise<FacebookAPIResponse> => {
  // Convert params to Record<string, string> format
  const stringParams: Record<string, string> | undefined = params ? {
    ...(params.date_preset && { date_preset: params.date_preset }),
    ...(params.time_range && { time_range: JSON.stringify(params.time_range) }),
    ...(params.fields && { fields: params.fields }),
  } : undefined;

  return facebookSDK.getCampaignInsights(campaignId, stringParams);
};

/**
 * Tạo campaign trực tiếp trên Facebook
 */
export const createFacebookCampaignDirect = async (
  accountId: string,
  campaignData: {
    name: string;
    objective: string;
    status?: string;
    daily_budget?: number;
    lifetime_budget?: number;
    start_time?: string;
    stop_time?: string;
    special_ad_categories?: string[];
  }
): Promise<FacebookAPIResponse> => {
  return facebookSDK.createCampaign(accountId, campaignData);
};

/**
 * Cập nhật campaign trực tiếp trên Facebook
 */
export const updateFacebookCampaignDirect = async (
  campaignId: string,
  campaignData: {
    name?: string;
    status?: string;
    daily_budget?: number;
    lifetime_budget?: number;
    start_time?: string;
    stop_time?: string;
  }
): Promise<FacebookAPIResponse> => {
  return facebookSDK.updateCampaign(campaignId, campaignData);
};

/**
 * Xóa campaign trực tiếp trên Facebook
 */
export const deleteFacebookCampaignDirect = async (
  campaignId: string
): Promise<FacebookAPIResponse> => {
  return facebookSDK.deleteCampaign(campaignId);
};

/**
 * Lấy ad sets trực tiếp từ Facebook
 */
export const getFacebookAdSetsDirect = async (
  campaignId: string,
  params?: {
    limit?: number;
    after?: string;
    before?: string;
    fields?: string;
  }
): Promise<FacebookAPIResponse> => {
  // Convert params to Record<string, string> format
  const stringParams: Record<string, string> | undefined = params ? {
    ...(params.limit !== undefined && { limit: params.limit.toString() }),
    ...(params.after && { after: params.after }),
    ...(params.before && { before: params.before }),
    ...(params.fields && { fields: params.fields }),
  } : undefined;

  return facebookSDK.getAdSets(campaignId, stringParams);
};

/**
 * Lấy ads trực tiếp từ Facebook
 */
export const getFacebookAdsDirect = async (
  adSetId: string,
  params?: {
    limit?: number;
    after?: string;
    before?: string;
    fields?: string;
  }
): Promise<FacebookAPIResponse> => {
  // Convert params to Record<string, string> format
  const stringParams: Record<string, string> | undefined = params ? {
    ...(params.limit !== undefined && { limit: params.limit.toString() }),
    ...(params.after && { after: params.after }),
    ...(params.before && { before: params.before }),
    ...(params.fields && { fields: params.fields }),
  } : undefined;

  return facebookSDK.getAds(adSetId, stringParams);
};

/**
 * Lấy custom audiences trực tiếp từ Facebook
 */
export const getFacebookCustomAudiencesDirect = async (
  accountId: string
): Promise<FacebookAPIResponse> => {
  return facebookSDK.getCustomAudiences(accountId);
};

/**
 * Tạo custom audience trực tiếp trên Facebook
 */
export const createFacebookCustomAudienceDirect = async (
  accountId: string,
  audienceData: {
    name: string;
    description?: string;
    subtype: string;
    customer_file_source?: string;
  }
): Promise<FacebookAPIResponse> => {
  return facebookSDK.createCustomAudience(accountId, audienceData);
};
