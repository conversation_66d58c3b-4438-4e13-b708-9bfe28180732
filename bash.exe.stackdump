Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFED8430000 ntdll.dll
7FFED7900000 KERNEL32.DLL
7FFED55D0000 KERNELBASE.dll
7FFED7C70000 USER32.dll
7FFED5520000 win32u.dll
000210040000 msys-2.0.dll
7FFED7AB0000 GDI32.dll
7FFED5DC0000 gdi32full.dll
7FFED59B0000 msvcp_win.dll
7FFED5EF0000 ucrtbase.dll
7FFED60C0000 advapi32.dll
7FFED8340000 msvcrt.dll
7FFED61C0000 sechost.dll
7FFED6090000 bcrypt.dll
7FFED75D0000 RPCRT4.dll
7FFED4C40000 CRYPTBASE.DLL
7FFED5550000 bcryptPrimitives.dll
7FFED6180000 IMM32.DLL
