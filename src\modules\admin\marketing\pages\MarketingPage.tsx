import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ResponsiveGrid } from '@/shared/components/common';
import ModuleCard from '@/modules/components/card/ModuleCard';
import { useCustomFields } from '../hooks';

/**
 * Marketing Dashboard Page
 * Displays an overview of marketing module features
 */
const MarketingPage: React.FC = () => {
  const { t } = useTranslation(['marketingAdmin', 'common']);

  // State to store counts
  const [, setCustomFieldCount] = useState<number>(0);

  // Call API to get custom fields list
  const { data: fieldData } = useCustomFields({
    page: 1,
    limit: 1,
  });

  // Update counts when data is available
  useEffect(() => {
    if (fieldData?.meta) {
      setCustomFieldCount(fieldData.meta.totalItems);
    }
  }, [fieldData]);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Audience Card */}
        <ModuleCard
          title={t('audience.title')}
          description={t('audience.description')}
          icon="users"
          linkTo="/admin/marketing/audience"
        />

        {/* Segment Card */}
        <ModuleCard
          title={t('marketingAdmin:segments.title', 'Quản lý phân đoạn')}
          description={t('segment.description')}
          icon="filter"
          linkTo="/admin/marketing/segments"
        />

        {/* Custom Fields Card */}
        <ModuleCard
          title={t('customFields.title', 'Quản lý trường tùy chỉnh')}
          description={t('customFields.description', 'Tạo và quản lý các trường dữ liệu tùy chỉnh')}
          icon="database"
          linkTo="/admin/marketing/custom-fields"
        />

        {/* Tags Card */}
        <ModuleCard
          title={t('tags.title', 'Quản lý tag')}
          description={t('tags.description', 'Tạo và quản lý các tag cho khách hàng')}
          icon="tag"
          linkTo="/admin/marketing/tags"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default MarketingPage;
