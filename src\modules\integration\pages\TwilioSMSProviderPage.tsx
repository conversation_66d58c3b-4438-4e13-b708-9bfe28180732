import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Card,
  Typography,
  Button,
  Input,
  Icon,
  FormItem,
  FormGrid,
} from '@/shared/components/common';
import { useTwilioSmsIntegration } from '../hooks/useTwilioSmsIntegration';
import {
  createTwilioSmsIntegrationSchema,
  CreateTwilioSmsIntegrationFormData
} from '../schemas/twilio-sms.schema';

// Provider info type
type ProviderInfo = { title: string; description: string; icon: string };

// Provider info mapping
const providerInfoMap: Record<string, ProviderInfo> = {
  twilio: {
    title: 'Twilio SMS',
    description: 'Dịch vụ SMS quốc tế hàng đầu với độ tin cậy cao',
    icon: 'message-circle',
  },
  ftp: {
    title: 'FTP SMS Brandname',
    description: 'Dịch vụ SMS Brandname của FTP Telecom',
    icon: 'smartphone',
  },
  vnpt: {
    title: 'VNPT SMS',
    description: 'Dịch vụ SMS của VNPT',
    icon: 'phone',
  },
};

/**
 * Trang tạo tích hợp SMS Provider cụ thể
 */
const TwilioSMSProviderPage: React.FC = () => {
  const { provider } = useParams<{ provider?: string }>();
  const navigate = useNavigate();

  // Default provider info
  const defaultProviderInfo: ProviderInfo = {
    title: 'Twilio SMS',
    description: 'Dịch vụ SMS quốc tế hàng đầu với độ tin cậy cao',
    icon: 'message-circle',
  };

  // Get provider info - ensure it's never undefined
  const getProviderInfo = (providerKey: string): ProviderInfo => {
    return providerInfoMap[providerKey] || defaultProviderInfo;
  };

  const providerInfo = getProviderInfo(provider || 'twilio');

  // Hooks
  const { createIntegration, isCreating } = useTwilioSmsIntegration();

  // Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateTwilioSmsIntegrationFormData>({
    resolver: zodResolver(createTwilioSmsIntegrationSchema),
    defaultValues: {
      integrationName: '',
      info: {
        TWILIO_AUTH_TOKEN: '',
        TWILIO_BASE_DOMAIN: '',
      },
    },
  });

  // Form handlers
  const onSubmit: SubmitHandler<CreateTwilioSmsIntegrationFormData> = async (data) => {
    console.log('Form submitted with data:', data);
    try {
      await createIntegration.mutateAsync(data);
      // Reset form after successful creation
      reset();
      // Navigate back to integrations list
      navigate('/integrations/my-integrations');
    } catch (error) {
      // Error is handled by the hook
      console.error('Form submission error:', error);
    }
  };

  // Debug form errors
  const onError = (errors: unknown) => {
    console.log('Form validation errors:', errors);
  };

  const handleCancel = () => {
    reset();
    navigate('/integrations');
  };

  // Helper function to safely get error message
  const getErrorMessage = (error: unknown): string => {
    if (!error) return '';
    if (typeof error === 'string') return error;
    if (error && typeof error === 'object' && error !== null && 'message' in error) {
      const message = (error as { message: unknown }).message;
      return typeof message === 'string' ? message : String(message);
    }
    return '';
  };



  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
            <Icon name={providerInfo.icon as string} size="lg" className="text-primary" />
          </div>
          <div>
            <Typography variant="h4" className="font-bold">
              {providerInfo.title}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {providerInfo.description}
            </Typography>
          </div>
        </div>

        {/* Form */}
        <Card className="p-6">
          <form onSubmit={handleSubmit(onSubmit, onError)} className="space-y-6">
            {/* Form Header */}
            <div className="border-b border-border pb-4">
              <Typography variant="h5" className="font-semibold">
                Tạo tích hợp Twilio SMS
              </Typography>
              <Typography variant="body2" className="text-muted-foreground mt-1">
                Điền thông tin để tạo tích hợp Twilio SMS mới
              </Typography>
            </div>

            {/* Form Fields */}
            <FormGrid columns={1} gap="lg">
              {/* Integration Name */}
              <FormItem
                label="Tên tích hợp"
                required
                helpText="Tên để nhận diện tích hợp này"
              >
                <Input
                  {...register('integrationName')}
                  placeholder="Ví dụ: Twilio Production SMS"
                  className="w-full"
                  error={getErrorMessage(errors.integrationName) || null}
                  helperText={getErrorMessage(errors.integrationName)}
                />
              </FormItem>

              {/* Twilio Auth Token */}
              <FormItem
                label="Twilio Auth Token"
                required
                helpText="Auth Token từ Twilio Console"
              >
                <Input
                  {...register('info.TWILIO_AUTH_TOKEN')}
                  type="password"
                  placeholder="Nhập Twilio Auth Token"
                  className="w-full"
                  error={getErrorMessage(errors.info?.TWILIO_AUTH_TOKEN) || null}
                  helperText={getErrorMessage(errors.info?.TWILIO_AUTH_TOKEN)}
                />
              </FormItem>

              {/* Twilio Base Domain */}
              <FormItem
                label="Twilio Base Domain"
                required
                helpText="Domain cơ sở của Twilio (ví dụ: api.twilio.com)"
              >
                <Input
                  {...register('info.TWILIO_BASE_DOMAIN')}
                  placeholder="Ví dụ: api.twilio.com"
                  className="w-full"
                  error={getErrorMessage(errors.info?.TWILIO_BASE_DOMAIN) || null}
                  helperText={getErrorMessage(errors.info?.TWILIO_BASE_DOMAIN)}
                />
              </FormItem>
            </FormGrid>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-4 pt-4 border-t border-border">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isCreating}
              >
                Hủy
              </Button>
              <Button
                type="submit"
                isLoading={isCreating}
                disabled={isCreating}
              >
                Tạo tích hợp
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default TwilioSMSProviderPage;
