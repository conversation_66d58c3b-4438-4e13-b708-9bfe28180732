import { useState } from 'react';

/**
 * Hook quản lý trạng thái hiển thị/ẩn form với animation
 * @returns Các hàm và state để quản lý form
 */
export const useSlideForm = (initialState = false) => {
  // State quản lý việc hiển thị form
  const [isVisible, setIsVisible] = useState(initialState);

  // Hàm hiển thị form
  const showForm = () => setIsVisible(true);

  // Hàm ẩn form
  const hideForm = () => setIsVisible(false);

  // Hàm toggle form
  const toggleForm = () => setIsVisible(prev => !prev);

  return {
    isVisible,
    showForm,
    hideForm,
    toggleForm,
  };
};

export default useSlideForm;
