/**
 * User Integration API Services
 * Các service để gọi API liên quan đến tích hợp của người dùng
 */

import { apiClient } from '@/shared/api';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  UserIntegrationDto,
  UserIntegrationQueryDto,
  CreateUserIntegrationDto,
  UpdateUserIntegrationDto,
  CreateTwilioSmsIntegrationDto,
} from '../types/user-integration.types';

const BASE_URL = '/user/integration';

/**
 * Lấy danh sách tích hợp của người dùng
 * @param params Query parameters
 * @returns Promise với danh sách tích hợp và thông tin phân trang
 */
export const getUserIntegrations = async (
  params: Partial<UserIntegrationQueryDto> = {}
): Promise<ApiResponseDto<PaginatedResult<UserIntegrationDto>>> => {
  const response = await apiClient.get<PaginatedResult<UserIntegrationDto>>(BASE_URL, {
    params,
    tokenType: 'user',
  });
  return response;
};

/**
 * Lấy thông tin chi tiết tích hợp theo ID
 * @param id ID của tích hợp
 * @returns Promise với thông tin chi tiết tích hợp
 */
export const getUserIntegrationById = async (
  id: string
): Promise<ApiResponseDto<UserIntegrationDto>> => {
  const response = await apiClient.get<UserIntegrationDto>(`${BASE_URL}/${id}`, {
    tokenType: 'user',
  });
  return response;
};

/**
 * Tạo tích hợp mới
 * @param data Dữ liệu tích hợp mới
 * @returns Promise với thông tin tích hợp đã tạo
 */
export const createUserIntegration = async (
  data: CreateUserIntegrationDto
): Promise<ApiResponseDto<UserIntegrationDto>> => {
  const response = await apiClient.post<UserIntegrationDto>(BASE_URL, data, {
    tokenType: 'user',
  });
  return response;
};

/**
 * Cập nhật tích hợp
 * @param id ID của tích hợp
 * @param data Dữ liệu cập nhật
 * @returns Promise với thông tin tích hợp đã cập nhật
 */
export const updateUserIntegration = async (
  id: string,
  data: UpdateUserIntegrationDto
): Promise<ApiResponseDto<UserIntegrationDto>> => {
  const response = await apiClient.put<UserIntegrationDto>(`${BASE_URL}/${id}`, data, {
    tokenType: 'user',
  });
  return response;
};

/**
 * Xóa tích hợp
 * @param id ID của tích hợp
 * @returns Promise với kết quả xóa
 */
export const deleteUserIntegration = async (
  id: string
): Promise<ApiResponseDto<{ success: boolean; message: string }>> => {
  const response = await apiClient.delete<{ success: boolean; message: string }>(`${BASE_URL}/${id}`, {
    tokenType: 'user',
  });
  return response;
};

/**
 * Kích hoạt/vô hiệu hóa tích hợp
 * @param id ID của tích hợp
 * @returns Promise với thông tin tích hợp đã cập nhật
 */
export const toggleUserIntegrationStatus = async (
  id: string
): Promise<ApiResponseDto<UserIntegrationDto>> => {
  const response = await apiClient.patch<UserIntegrationDto>(`${BASE_URL}/${id}/toggle-status`, {}, {
    tokenType: 'user',
  });
  return response;
};

/**
 * Kiểm tra kết nối tích hợp
 * @param id ID của tích hợp
 * @returns Promise với kết quả kiểm tra kết nối
 */
export const testUserIntegrationConnection = async (
  id: string
): Promise<ApiResponseDto<{ success: boolean; message: string; responseTime?: number }>> => {
  const response = await apiClient.post<{ success: boolean; message: string; responseTime?: number }>(
    `${BASE_URL}/${id}/test-connection`,
    {},
    {
      tokenType: 'user',
    }
  );
  return response;
};

/**
 * Lấy thống kê tích hợp của người dùng
 * @returns Promise với thống kê tích hợp
 */
export const getUserIntegrationStats = async (): Promise<ApiResponseDto<{
  total: number;
  active: number;
  inactive: number;
  pending: number;
  error: number;
  byType: Record<string, number>;
}>> => {
  const response = await apiClient.get<{
    total: number;
    active: number;
    inactive: number;
    pending: number;
    error: number;
    byType: Record<string, number>;
  }>(`${BASE_URL}/stats`, {
    tokenType: 'user',
  });
  return response;
};

/**
 * Tạo tích hợp Twilio SMS mới
 * @param data Dữ liệu tích hợp Twilio SMS
 * @returns Promise với thông tin tích hợp đã tạo
 */
export const createTwilioSmsIntegration = async (
  data: CreateTwilioSmsIntegrationDto
): Promise<ApiResponseDto<UserIntegrationDto>> => {
  const response = await apiClient.post<UserIntegrationDto>(`${BASE_URL}/twilio/sms`, data, {
    tokenType: 'user',
  });
  return response;
};

/**
 * Export tất cả các service functions
 */
export const UserIntegrationService = {
  getUserIntegrations,
  getUserIntegrationById,
  createUserIntegration,
  updateUserIntegration,
  deleteUserIntegration,
  toggleUserIntegrationStatus,
  testUserIntegrationConnection,
  getUserIntegrationStats,
};

export default UserIntegrationService;
