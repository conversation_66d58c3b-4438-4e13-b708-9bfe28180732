/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import Editor from 'react-simple-code-editor';
import { highlight, languages } from 'prismjs';
import 'prismjs/components/prism-json';
import 'prismjs/themes/prism.css';

interface JsonEditorProps {
  value: Record<string, unknown>;
  onChange: (value: Record<string, unknown>) => void;
  onError?: (error: string | null) => void;
  placeholder?: string;
  disabled?: boolean;
  minHeight?: number;
  className?: string;
}

/**
 * Component hiển thị editor cho JSON với syntax highlighting
 */
const JsonEditor: React.FC<JsonEditorProps> = ({
  value,
  onChange,
  onError,
  placeholder = 'Enter JSON...',
  disabled = false,
  minHeight = 200,
  className = '',
}) => {
  // Chuyển đổi object thành string để hiển thị
  const [code, setCode] = useState<string>(() => {
    try {
      return JSON.stringify(value, null, 2);
    } catch (error) {
      console.error('Error formatting JSON:', error);
      return '';
    }
  });

  // Cập nhật code khi value thay đổi từ bên ngoài
  useEffect(() => {
    try {
      const formatted = JSON.stringify(value, null, 2);
      if (formatted !== code) {
        setCode(formatted);
      }
    } catch (error) {
      console.error('Error formatting JSON:', error);
    }
  }, [value]);

  // Xử lý khi người dùng thay đổi code
  const handleCodeChange = (newCode: string) => {
    setCode(newCode);

    try {
      // Thử parse JSON
      const parsedJson = JSON.parse(newCode);
      onChange(parsedJson);
      if (onError) onError(null);
    } catch (error) {
      // Nếu có lỗi, chỉ cập nhật lỗi mà không cập nhật giá trị
      if (onError) onError((error as Error).message);
    }
  };

  return (
    <div
      className={`border rounded-md overflow-auto ${className} ${
        disabled ? 'bg-gray-100 dark:bg-gray-800 opacity-70' : 'bg-white dark:bg-gray-900'
      }`}
      style={{ minHeight }}
    >
      <Editor
        value={code}
        onValueChange={handleCodeChange}
        highlight={code =>
          highlight(code, languages['json'] || languages['javascript'] || {}, 'json')
        }
        padding={10}
        style={{
          fontFamily: '"Fira code", "Fira Mono", monospace',
          fontSize: 14,
          minHeight: minHeight - 20, // Trừ đi padding
        }}
        disabled={disabled}
        placeholder={placeholder}
        className="w-full"
        tabSize={2}
        insertSpaces={true}
        ignoreTabKey={false}
        textareaId="json-editor"
        textareaClassName="focus:outline-none"
        preClassName="focus:outline-none"
      />
    </div>
  );
};

export default JsonEditor;
