import React from 'react';
import { useTranslation } from 'react-i18next';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { Card, Typography } from '@/shared/components/common';

// Mock data để test
const mockLoadOptions = async (params: { search?: string; page?: number; limit?: number }) => {
  console.log('🔍 Mock loadOptions called with:', params);
  
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const allOptions = [
    { value: 'email', label: 'Email', description: 'Trường hệ thống' },
    { value: 'name', label: 'Tên', description: 'Trường hệ thống' },
    { value: 'phone', label: 'Số điện thoại', description: 'Trường hệ thống' },
    { value: 'custom_field_1', label: 'Tuổi', description: 'Trường tùy chỉnh' },
    { value: 'custom_field_2', label: '<PERSON><PERSON><PERSON> nghiệp', description: 'Trường tùy chỉnh' },
    { value: 'custom_field_3', label: 'Sở thích', description: 'Trường tùy chỉnh' },
  ];

  // Filter by search
  const filteredOptions = params.search 
    ? allOptions.filter(opt => 
        opt.label.toLowerCase().includes(params.search!.toLowerCase()) ||
        opt.value.toLowerCase().includes(params.search!.toLowerCase())
      )
    : allOptions;

  const page = params.page || 1;
  const limit = params.limit || 20;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const items = filteredOptions.slice(startIndex, endIndex);

  const result = {
    items,
    totalItems: filteredOptions.length,
    totalPages: Math.ceil(filteredOptions.length / limit),
    currentPage: page,
  };

  console.log('📤 Mock loadOptions result:', result);
  return result;
};

/**
 * Test component để kiểm tra AsyncSelectWithPagination với selected value
 */
const TestSegmentForm: React.FC = () => {
  // Chỉ import hook useTranslation mà không lấy biến 't' ra
  useTranslation(['marketing', 'common']);
  const [selectedField, setSelectedField] = React.useState<string>('email'); // Pre-selected value

  const handleFieldChange = (value: string | string[] | number | number[] | undefined) => {
    console.log('🎯 Field changed to:', value);
    setSelectedField(value as string);
  };

  return (
    <div className="w-full bg-background text-foreground p-6">
      <Card className="max-w-2xl mx-auto p-6">
        <Typography variant="h4" className="mb-6">
          Test AsyncSelectWithPagination - Selected Value Display
        </Typography>

        <div className="space-y-6">
          <div>
            <Typography variant="h6" className="mb-2">
              Test Case 1: Pre-selected value = "email"
            </Typography>
            <Typography variant="body2" className="mb-4 text-muted-foreground">
              Component should display "Email" label immediately, not just the value "email"
            </Typography>
            
            <AsyncSelectWithPagination
              value={selectedField}
              onChange={handleFieldChange}
              loadOptions={mockLoadOptions}
              placeholder="Chọn trường..."
              searchOnEnter={false}
              autoLoadInitial={true}
              debounceTime={300}
              itemsPerPage={20}
              noOptionsMessage="Không tìm thấy tùy chọn"
              loadingMessage="Đang tải..."
              fullWidth
            />
          </div>

          <div>
            <Typography variant="body2">
              <strong>Current selected value:</strong> {selectedField || 'None'}
            </Typography>
          </div>

          <div className="space-y-2">
            <Typography variant="h6">Test Actions:</Typography>
            <div className="flex gap-2 flex-wrap">
              <button
                onClick={() => setSelectedField('name')}
                className="px-3 py-1 bg-primary text-primary-foreground rounded text-sm"
              >
                Set to "name"
              </button>
              <button
                onClick={() => setSelectedField('phone')}
                className="px-3 py-1 bg-primary text-primary-foreground rounded text-sm"
              >
                Set to "phone"
              </button>
              <button
                onClick={() => setSelectedField('custom_field_1')}
                className="px-3 py-1 bg-primary text-primary-foreground rounded text-sm"
              >
                Set to "custom_field_1"
              </button>
              <button
                onClick={() => setSelectedField('')}
                className="px-3 py-1 bg-secondary text-secondary-foreground rounded text-sm"
              >
                Clear selection
              </button>
            </div>
          </div>

          <div className="bg-muted p-4 rounded">
            <Typography variant="body2" className="font-mono text-sm">
              Expected behavior:
              <br />• Component should show proper label immediately when value is set
              <br />• Should not show raw value like "email" but display "Email"
              <br />• Should work even before dropdown is opened
              <br />• Should cache selected options for future display
            </Typography>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TestSegmentForm;
