/**
 * Hooks for segment API using TanStack Query
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { SegmentService } from '../services/segment.service';
import {
  CreateSegmentRequest,
  SegmentQueryParams,
  UpdateSegmentRequest,
} from '../types/segment.types';

/**
 * Query keys for segment API
 */
export const SEGMENT_QUERY_KEYS = {
  all: ['marketing', 'segments'] as const,
  list: (params: SegmentQueryParams) => [...SEGMENT_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...SEGMENT_QUERY_KEYS.all, 'detail', id] as const,
  stats: (id: number) => [...SEGMENT_QUERY_KEYS.all, 'stats', id] as const,
};

/**
 * Hook to get segments with pagination and filtering
 */
export const useSegments = (params: SegmentQueryParams = {}) => {
  return useQuery({
    queryKey: SEGMENT_QUERY_KEYS.list(params),
    queryFn: () => SegmentService.getSegments(params),
    select: data => data.result,
  });
};

/**
 * Hook to get segment by ID
 */
export const useSegment = (id: number) => {
  return useQuery({
    queryKey: SEGMENT_QUERY_KEYS.detail(id),
    queryFn: () => SegmentService.getSegmentById(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook to get segment statistics
 */
export const useSegmentStats = (id: number) => {
  return useQuery({
    queryKey: SEGMENT_QUERY_KEYS.stats(id),
    queryFn: () => SegmentService.getSegmentStats(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook to create segment
 */
export const useCreateSegment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSegmentRequest) => SegmentService.createSegment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to update segment
 */
export const useUpdateSegment = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateSegmentRequest) => SegmentService.updateSegment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.stats(id) });
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to delete segment
 */
export const useDeleteSegment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => SegmentService.deleteSegment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to delete multiple segments
 */
export const useBulkDeleteSegments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: number[]) => SegmentService.deleteMultipleSegments(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.all });
    },
  });
};
