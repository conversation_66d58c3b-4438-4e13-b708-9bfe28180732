/**
 * Hook quản lý dữ liệu gói R-Point từ API
 */
import { useMemo } from 'react';
import { useRPointPackages as useRPointPackagesQuery } from './useRPointQuery';
import { RPointPackage, CustomRPointPackageProps } from '../types/rpoint.types';

/**
 * Hook cung cấp dữ liệu và logic cho các gói R-Point từ API
 */
export const useRPointPackagesData = () => {
  // Gọi API để lấy danh sách gói R-Point
  const { data: apiPackages, isLoading, error } = useRPointPackagesQuery();

  // Chuyển đổi dữ liệu từ API sang định dạng sử dụng trong UI
  const packages = useMemo<RPointPackage[]>(() => {
    if (!apiPackages) return [];

    console.log('API Packages:', apiPackages); // Log để debug

    return apiPackages
      .filter(pkg => !pkg.isCustomize)
      .map(pkg => ({
        id: pkg.id.toString(),
        points: typeof pkg.point === 'string' ? parseInt(pkg.point, 10) : pkg.point,
        price: pkg.cash,
        ...(pkg.description && { description: pkg.description }),
        isPopular: pkg.name.toLowerCase().includes('phổ biến'),
      }));
  }, [apiPackages]);

  // Lấy thông tin gói tùy chỉnh từ API
  const customPackage = useMemo<CustomRPointPackageProps>(() => {
    if (!apiPackages) {
      return {
        rate: 3,
        minPoints: 10,
        maxPoints: 100000,
      };
    }

    const customPkg = apiPackages.find(pkg => pkg.isCustomize);

    if (!customPkg) {
      return {
        rate: 3,
        minPoints: 10,
        maxPoints: 100000,
      };
    }

    // Đảm bảo rate không null
    const safeRate = customPkg.rate || 3;

    // Tính toán số R-Point tối thiểu và tối đa dựa trên min/max cash
    const minPoints = customPkg.min ? Math.ceil(customPkg.min / safeRate) : 10;
    const maxPoints = customPkg.max ? Math.floor(customPkg.max / safeRate) : 100000;

    return {
      rate: safeRate,
      minPoints,
      maxPoints,
    };
  }, [apiPackages]);

  return {
    packages,
    customPackage,
    isLoading,
    error,
  };
};
