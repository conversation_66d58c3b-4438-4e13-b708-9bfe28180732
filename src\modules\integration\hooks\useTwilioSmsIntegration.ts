/**
 * Hook for Twilio SMS Integration
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { NotificationUtil } from '@/shared/utils/notification';
import { CreateTwilioSmsIntegrationDto } from '../types/user-integration.types';
import { createTwilioSmsIntegration } from '../services/user-integration.api';

/**
 * Hook for creating Twilio SMS integration
 */
export const useTwilioSmsIntegration = () => {
  const queryClient = useQueryClient();

  // Create Twilio SMS integration mutation
  const createIntegration = useMutation({
    mutationFn: (data: CreateTwilioSmsIntegrationDto) => createTwilioSmsIntegration(data),
    onSuccess: (response) => {
      // Show success notification
      NotificationUtil.success({
        title: 'Tạo tích hợp Twilio SMS thành công',
        message: `Tích hợp "${response.result.integrationName}" đã được tạo thành công.`,
      });

      // Invalidate and refetch user integrations list
      queryClient.invalidateQueries({
        queryKey: ['user-integrations'],
      });
    },
    onError: (error: Error) => {
      // Show error notification
      NotificationUtil.error({
        title: 'Tạo tích hợp Twilio SMS thất bại',
        message: error.message || 'Đã xảy ra lỗi khi tạo tích hợp. Vui lòng thử lại.',
      });
      console.error('Create Twilio SMS integration error:', error);
    },
  });

  return {
    // Mutations
    createIntegration,
    
    // Loading states
    isCreating: createIntegration.isPending,
    
    // Error states
    createError: createIntegration.error,
  };
};
