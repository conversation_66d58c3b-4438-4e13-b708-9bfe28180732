import { z } from 'zod';
import { CampaignStatus, CampaignType } from '../types/campaign.types';

/**
 * Schema validation cho campaign
 */
export const campaignSchema = z.object({
  name: z
    .string()
    .min(2, 'Tên phải có ít nhất 2 ký tự')
    .max(100, 'Tên không được vượt quá 100 ký tự'),
  description: z.string().max(500, 'Mô tả không được vượt quá 500 ký tự').optional(),
  type: z.nativeEnum(CampaignType),
  status: z.nativeEnum(CampaignStatus).default(CampaignStatus.DRAFT),
  segmentId: z.string().min(1, 'Segment là bắt buộc'),
  startDate: z.string().min(1, 'Ngày bắt đầu là bắt buộc'),
  endDate: z.string().optional(),
});

export type CampaignFormValues = z.infer<typeof campaignSchema>;
