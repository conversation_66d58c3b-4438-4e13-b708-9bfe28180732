import React, { useState, useRef } from 'react';
import { FieldValues } from 'react-hook-form';
import { z } from 'zod';
import {
  Card,
  Input,
  Form,
  FormItem,
  IconCard,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Interface cho cấu hình FPT SMS
 */
export interface FptSmsConfig {
  /**
   * Tên tích hợp
   */
  integrationName: string;

  /**
   * Client ID dùng cho xác thực OAuth
   */
  clientId: string;

  /**
   * Client Secret dùng cho xác thực OAuth
   */
  clientSecret: string;

  /**
   * Brandname mặc định
   */
  brandName?: string;
}

/**
 * Schema validation cho FPT SMS
 */
const fptSmsConfigSchema = z.object({
  integrationName: z.string().min(1, 'Tên tích hợp là bắt buộc'),
  clientId: z.string().min(1, 'Client ID là bắt buộc'),
  clientSecret: z.string().min(1, 'Client Secret là bắt buộc'),
  brandName: z.string().optional(),
});

type FptSmsFormData = z.infer<typeof fptSmsConfigSchema>;

/**
 * Trang tích hợp FPT SMS
 */
const SmsIntegrationPage: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<FormRef<FieldValues>>(null);

  // Handle form submission
  const handleSubmit = async (data: FieldValues) => {
    const formData = data as FptSmsFormData;
    setIsSubmitting(true);

    try {
      // TODO: Implement API call to save FPT SMS configuration
      console.log('FPT SMS Configuration:', formData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      NotificationUtil.success({
        message: 'Cấu hình FPT SMS thành công!'
      });
    } catch (error: unknown) {
      console.error('Lỗi cấu hình FPT SMS:', error);
      const errorMessage = error instanceof Error ? error.message : 'Không thể cấu hình FPT SMS';
      NotificationUtil.error({
        message: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    // TODO: Navigate back or reset form
    console.log('Cancel FPT SMS configuration');
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Form Section */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <div className="space-y-6">
                <Form
                  ref={formRef}
                  schema={fptSmsConfigSchema}
                  onSubmit={handleSubmit}
                  defaultValues={{
                    integrationName: '',
                    clientId: '',
                    clientSecret: '',
                    brandName: '',
                  }}
                >
                  <div className="space-y-6">
                    {/* Integration Name */}
                    <FormItem
                      name="integrationName"
                      label="Tên tích hợp *"
                      required
                    >
                      <Input
                        placeholder="Nhập tên cho tích hợp này"
                        disabled={isSubmitting}
                        fullWidth
                      />
                    </FormItem>

                    {/* Client ID */}
                    <FormItem
                      name="clientId"
                      label="Client ID *"
                      required
                    >
                      <Input
                        placeholder="Nhập Client ID từ FPT SMS"
                        disabled={isSubmitting}
                        fullWidth
                      />
                    </FormItem>

                    {/* Client Secret */}
                    <FormItem
                      name="clientSecret"
                      label="Client Secret *"
                      required
                    >
                      <Input
                        placeholder="Nhập Client Secret từ FPT SMS"
                        type="password"
                        disabled={isSubmitting}
                        fullWidth
                      />
                    </FormItem>

                    {/* Brand Name */}
                    <FormItem
                      name="brandName"
                      label="Brandname"
                    >
                      <Input
                        placeholder="Nhập brandname mặc định (tùy chọn)"
                        disabled={isSubmitting}
                        fullWidth
                      />
                    </FormItem>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-4">
                      <IconCard
                        icon="x"
                        title="Hủy"
                        onClick={handleCancel}
                        className="cursor-pointer"
                        disabled={isSubmitting}
                      />
                      <IconCard
                        icon="check"
                        title={isSubmitting ? "Đang lưu..." : "Lưu cấu hình"}
                        onClick={() => formRef.current?.submit()}
                        variant="primary"
                        disabled={isSubmitting}
                        className="cursor-pointer"
                      />
                    </div>
                  </div>
                </Form>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmsIntegrationPage;
