import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { NotificationUtil } from '@/shared/utils/notification';

import DatasetFormGoogle from '../components/DatasetFormGoogle';

/**
 * Page để tạo dataset cho Google
 * Sử dụng DatasetFormGoogle (không có ConversationSidebar)
 */
const CreateDatasetGooglePage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleSuccess = () => {
    // Show success notification
    NotificationUtil.success({
      title: t('admin-dataset:createDataset.notification.success.title', 'Thành công'),
      message: t(
        'admin-dataset:createDataset.notification.success.messageGoogle',
        'Dataset Google đã được tạo thành công!'
      ),
      duration: 5000,
    });

    // Navigate back to dataset list
    navigate('/admin/dataset/data-fine-tune');
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <DatasetFormGoogle onSuccess={handleSuccess} />
      </div>
    </div>
  );
};

export default CreateDatasetGooglePage;
