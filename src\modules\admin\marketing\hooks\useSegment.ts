import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { SegmentService } from '../services/segment.service';
import { SegmentQueryParams, CreateSegmentRequest, UpdateSegmentRequest } from '../types/segment.types';

/**
 * Interface cho tham số filter của segment hook
 * Extends SegmentQueryParams để đảm bảo tương thích
 */
type SegmentFilterParams = SegmentQueryParams;

// Key cho React Query
const SEGMENT_QUERY_KEY = 'segments';

/**
 * Hook để lấy danh sách segment
 */
export const useSegments = (params?: SegmentFilterParams) => {
  return useQuery({
    queryKey: [SEGMENT_QUERY_KEY, params],
    queryFn: () => SegmentService.getSegments(params || {}), // Provide empty object as default
    select: data => data.result,
  });
};

/**
 * Hook để lấy chi tiết segment
 */
export const useSegment = (id: number) => {
  return useQuery({
    queryKey: [SEGMENT_QUERY_KEY, id],
    queryFn: () => SegmentService.getSegmentById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo segment mới
 */
export const useCreateSegment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSegmentRequest) => SegmentService.createSegment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [SEGMENT_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật segment
 */
export const useUpdateSegment = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateSegmentRequest) => SegmentService.updateSegment(id, data),
    onSuccess: updatedSegment => {
      queryClient.setQueryData([SEGMENT_QUERY_KEY, id], updatedSegment);
      queryClient.invalidateQueries({ queryKey: [SEGMENT_QUERY_KEY] });
    },
  });
};

/**
 * Hook để xóa segment
 */
export const useDeleteSegment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => SegmentService.deleteSegment(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: [SEGMENT_QUERY_KEY, id] });
      queryClient.invalidateQueries({ queryKey: [SEGMENT_QUERY_KEY] });
    },
  });
};
