import { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components/common';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { lazy } from 'react';
const CustomFieldsPage = lazy(() => import('../pages/CustomFieldsPage'));
const MarketingPage = lazy(() => import('../pages/MarketingPage'));
const AudiencePage = lazy(() => import('../pages/AudiencePage'));
const AdminSegmentPage = lazy(() => import('../pages/AdminSegmentPage'));
const AdminTagPage = lazy(() => import('../pages/AdminTagPage'));

export const marketingAdminRoutes: RouteObject[] = [
  {
    path: '/admin/marketing',
    element: (
      <AdminLayout title="Quản lý marketing">
        <Suspense fallback={<Loading />}>
          <MarketingPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/marketing/custom-fields',
    element: (
      <AdminLayout title="Tạo trường tùy chỉnh">
        <Suspense fallback={<Loading />}>
          <CustomFieldsPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/marketing/audience',
    element: (
      <AdminLayout title="Quản lý đối tượng">
        <Suspense fallback={<Loading />}>
          <AudiencePage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/marketing/segments',
    element: (
      <AdminLayout title="Quản lý phân đoạn">
        <Suspense fallback={<Loading />}>
          <AdminSegmentPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/marketing/tags',
    element: (
      <AdminLayout title="Quản lý Tag">
        <Suspense fallback={<Loading />}>
          <AdminTagPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];
