import { z } from 'zod';
import { AudienceStatus, AudienceType } from '../types/audience.types';

/**
 * Schema validation cho thuộc tính audience
 */
export const audienceAttributeSchema = z.object({
  name: z.string().min(1, 'Tên thuộc tính là bắt buộc'),
  value: z.string().min(1, 'Giá trị thuộc tính là bắt buộc'),
});

/**
 * Schema validation cho audience
 */
export const audienceSchema = z.object({
  name: z
    .string()
    .min(2, 'Tên phải có ít nhất 2 ký tự')
    .max(100, 'Tên không được vượt quá 100 ký tự'),
  description: z.string().max(500, 'Mô tả không được vượt quá 500 ký tự').optional(),
  type: z.nativeEnum(AudienceType),
  status: z.nativeEnum(AudienceStatus).default(AudienceStatus.DRAFT),
  attributes: z.array(audienceAttributeSchema).optional(),
});

export type AudienceFormValues = z.infer<typeof audienceSchema>;
