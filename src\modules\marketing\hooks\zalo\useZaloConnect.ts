import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ZaloService } from '../../services/zalo.service';
import type { ConnectOfficialAccountDto, OfficialAccountResponseDto } from '../../types/zalo.types';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Hook để kết nối Official Account với hệ thống
 */
export function useConnectOfficialAccount() {
  const queryClient = useQueryClient();

  return useMutation<
    ApiResponseDto<OfficialAccountResponseDto>,
    Error,
    ConnectOfficialAccountDto
  >({
    mutationFn: (data: ConnectOfficialAccountDto) => 
      ZaloService.connectOfficialAccount(data),
    
    onSuccess: (response) => {
      // Invalidate và refetch các queries liên quan
      queryClient.invalidateQueries({ queryKey: ['zalo', 'accounts'] });
      queryClient.invalidateQueries({ queryKey: ['zalo', 'official-accounts'] });
      
      // Có thể cache thông tin account mới được kết nối
      if (response.result) {
        queryClient.setQueryData(
          ['zalo', 'account', response.result.id],
          response.result
        );
      }
    },
    
    onError: (error) => {
      console.error('Lỗi khi kết nối Official Account:', error);
    },
  });
}
