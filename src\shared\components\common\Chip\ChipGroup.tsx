import React, { useState, useCallback } from 'react';
import Chip, { ChipProps } from './Chip';
import { TransitionGroup } from 'react-transition-group';

export interface ChipData {
  id: string | number;
  label: React.ReactNode;
  variant?: ChipProps['variant'];
  leftIconName?: ChipProps['leftIconName'];
  rightIconName?: ChipProps['rightIconName'];
  avatarSrc?: string;
  disabled?: boolean;
}

export interface ChipGroupProps {
  /**
   * Danh sách các chip
   */
  chips: ChipData[];

  /**
   * Kích thước của các chip
   */
  size?: ChipProps['size'];

  /**
   * Cho phép xóa chip
   */
  closable?: boolean;

  /**
   * Hàm xử lý khi xóa chip
   */
  onDelete?: (chipId: string | number) => void;

  /**
   * Cho phép chọn nhiều chip
   */
  multiSelect?: boolean;

  /**
   * <PERSON>h sách ID của các chip đã chọn
   */
  selectedChips?: (string | number)[];

  /**
   * Hàm xử lý khi chọn/bỏ chọn chip
   */
  onSelectionChange?: (selectedChips: (string | number)[]) => void;

  /**
   * Hiệu ứng khi thêm/xóa chip
   */
  animation?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * ChipGroup component quản lý nhiều chip
 *
 * @example
 * // ChipGroup cơ bản
 * <ChipGroup
 *   chips={[
 *     { id: 1, label: 'Chip 1' },
 *     { id: 2, label: 'Chip 2' },
 *     { id: 3, label: 'Chip 3' },
 *   ]}
 * />
 *
 * @example
 * // ChipGroup với closable
 * <ChipGroup
 *   chips={[
 *     { id: 1, label: 'Chip 1' },
 *     { id: 2, label: 'Chip 2' },
 *   ]}
 *   closable
 *   onDelete={(id) => console.log(`Deleted chip ${id}`)}
 * />
 *
 * @example
 * // ChipGroup với multiSelect
 * <ChipGroup
 *   chips={[
 *     { id: 1, label: 'Chip 1' },
 *     { id: 2, label: 'Chip 2' },
 *   ]}
 *   multiSelect
 *   selectedChips={[1]}
 *   onSelectionChange={(selected) => console.log('Selected chips:', selected)}
 * />
 */
const ChipGroup: React.FC<ChipGroupProps> = ({
  chips,
  size = 'md',
  closable = false,
  onDelete,
  multiSelect = false,
  selectedChips = [],
  onSelectionChange,
  animation = false,
  className = '',
}) => {
  // Local state for selected chips if not controlled
  const [localSelectedChips, setLocalSelectedChips] = useState<(string | number)[]>(selectedChips);

  // Use controlled or uncontrolled selected chips
  const effectiveSelectedChips = onSelectionChange ? selectedChips : localSelectedChips;

  // Handle chip selection
  const handleChipSelect = useCallback(
    (chipId: string | number, selected: boolean) => {
      const newSelectedChips = selected
        ? [...effectiveSelectedChips, chipId]
        : effectiveSelectedChips.filter(id => id !== chipId);

      if (onSelectionChange) {
        onSelectionChange(newSelectedChips);
      } else {
        setLocalSelectedChips(newSelectedChips);
      }
    },
    [effectiveSelectedChips, onSelectionChange]
  );

  // Handle chip deletion
  const handleChipDelete = useCallback(
    (chipId: string | number) => {
      if (onDelete) {
        onDelete(chipId);
      }
    },
    [onDelete]
  );

  // Render chips
  const renderChips = () => {
    return chips.map(chip => (
      <Chip
        key={chip.id}
        id={chip.id}
        variant={chip.variant || 'default'}
        size={size}
        {...(chip.leftIconName && { leftIconName: chip.leftIconName })}
        {...(chip.rightIconName && { rightIconName: chip.rightIconName })}
        {...(chip.avatarSrc && { avatarSrc: chip.avatarSrc })}
        {...(chip.disabled && { disabled: chip.disabled })}
        closable={closable}
        {...(closable && { onClose: () => handleChipDelete(chip.id) })}
        {...(multiSelect && { isSelected: effectiveSelectedChips.includes(chip.id) })}
        {...(multiSelect && { onSelect: (selected: boolean) => handleChipSelect(chip.id, selected) })}
        animation={animation}
        className="m-1"
      >
        {chip.label}
      </Chip>
    ));
  };

  return (
    <div className={`flex flex-wrap -m-1 ${className}`}>
      {animation ? (
        <TransitionGroup component={null}>{renderChips()}</TransitionGroup>
      ) : (
        renderChips()
      )}
    </div>
  );
};

export default ChipGroup;
