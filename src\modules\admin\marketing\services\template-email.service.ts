/**
 * Service for template email API - Layer 1: Raw API calls
 * Matches backend API specification: POST /api/v1/marketing/template-emails
 */

import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  CreateTemplateEmailRequest,
  TemplateEmailDetailResponse,
  TemplateEmailListResponse,
  TemplateEmailQueryParams,
  UpdateTemplateEmailRequest,
  TemplateEmailOverviewResponse,
} from '../types/template-email.types';

/**
 * Base URL for template email API - matches backend controller
 * Backend endpoint: /api/v1/marketing/template-emails
 */
const BASE_URL = '/marketing/template-emails';

/**
 * Template email service
 */
export const TemplateEmailService = {
  /**
   * Get template emails with pagination and filtering
   */
  getTemplateEmails: async (
    params?: TemplateEmailQueryParams
  ): Promise<TemplateEmailListResponse> => {
    return apiClient.get<TemplateEmailListResponse['result']>(BASE_URL, { params });
  },

  /**
   * Get template email by ID
   */
  getTemplateEmailById: async (id: number): Promise<TemplateEmailDetailResponse> => {
    return apiClient.get<TemplateEmailDetailResponse['result']>(`${BASE_URL}/${id}`);
  },

  /**
   * Create template email
   */
  createTemplateEmail: async (
    data: CreateTemplateEmailRequest
  ): Promise<TemplateEmailDetailResponse> => {
    return apiClient.post<TemplateEmailDetailResponse['result']>(BASE_URL, data);
  },

  /**
   * Update template email
   */
  updateTemplateEmail: async (
    id: number,
    data: UpdateTemplateEmailRequest
  ): Promise<TemplateEmailDetailResponse> => {
    return apiClient.put<TemplateEmailDetailResponse['result']>(`${BASE_URL}/${id}`, data);
  },

  /**
   * Delete template email
   */
  deleteTemplateEmail: async (id: number): Promise<ApiResponseDto<{ success: boolean }>> => {
    return apiClient.delete<{ success: boolean }>(`${BASE_URL}/${id}`);
  },

  /**
   * Bulk delete template emails
   */
  bulkDeleteTemplateEmails: async (ids: number[]): Promise<ApiResponseDto<{ success: boolean }>> => {
    return apiClient.delete<{ success: boolean }>(`${BASE_URL}/bulk`, {
      data: { ids }
    });
  },

  /**
   * Get template email overview statistics
   */
  getOverview: async (): Promise<TemplateEmailOverviewResponse> => {
    return apiClient.get<TemplateEmailOverviewResponse['result']>(`${BASE_URL}/overview`);
  },
};
