import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/shared/api';
import type {
  ZaloAdsMetrics,
  ZaloAdsDateRange,
  ZaloAdsTopCampaign,
  ZaloAdsPerformanceTrend
} from '../../types/zalo-ads.types';

/**
 * Hook để lấy metrics tổng quan của Zalo Ads
 */
export function useZaloAdsMetrics(dateRange?: ZaloAdsDateRange) {
  return useQuery({
    queryKey: ['zalo-ads-metrics', dateRange],
    queryFn: async (): Promise<ZaloAdsMetrics> => {
      const params = new URLSearchParams();

      if (dateRange?.startDate) {
        params.append('startDate', dateRange.startDate);
      }
      if (dateRange?.endDate) {
        params.append('endDate', dateRange.endDate);
      }

      const response = await apiClient.get<ZaloAdsMetrics>(
        `/marketing/zalo-ads/metrics?${params.toString()}`
      );

      return response.result;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Hook để lấy metrics theo thời gian thực
 */
export function useZaloAdsRealtimeMetrics(
  enabled: boolean = true,
  refetchInterval: number = 30000 // 30 seconds
) {
  return useQuery({
    queryKey: ['zalo-ads-realtime-metrics'],
    queryFn: async (): Promise<ZaloAdsMetrics> => {
      const response = await apiClient.get<ZaloAdsMetrics>(
        '/marketing/zalo-ads/metrics/realtime'
      );

      return response.result;
    },
    enabled,
    refetchInterval,
    staleTime: 0, // Always consider stale for real-time data
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
}

/**
 * Hook để lấy metrics theo campaign
 */
export function useZaloAdsCampaignMetrics(
  campaignId: string,
  dateRange?: ZaloAdsDateRange
) {
  return useQuery({
    queryKey: ['zalo-ads-campaign-metrics', campaignId, dateRange],
    queryFn: async (): Promise<ZaloAdsMetrics> => {
      const params = new URLSearchParams();

      if (dateRange?.startDate) {
        params.append('startDate', dateRange.startDate);
      }
      if (dateRange?.endDate) {
        params.append('endDate', dateRange.endDate);
      }

      const response = await apiClient.get<ZaloAdsMetrics>(
        `/marketing/zalo-ads/campaigns/${campaignId}/metrics?${params.toString()}`
      );

      return response.result;
    },
    enabled: !!campaignId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

/**
 * Hook để lấy top performing campaigns
 */
export function useZaloAdsTopCampaigns(limit: number = 5) {
  return useQuery({
    queryKey: ['zalo-ads-top-campaigns', limit],
    queryFn: async (): Promise<ZaloAdsTopCampaign[]> => {
      const response = await apiClient.get<ZaloAdsTopCampaign[]>(
        `/marketing/zalo-ads/campaigns/top?limit=${limit}`
      );

      return response.result;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  });
}

/**
 * Hook để lấy performance trends
 */
export function useZaloAdsPerformanceTrends(
  period: 'day' | 'week' | 'month' = 'day',
  days: number = 30
) {
  return useQuery({
    queryKey: ['zalo-ads-performance-trends', period, days],
    queryFn: async (): Promise<ZaloAdsPerformanceTrend[]> => {
      const response = await apiClient.get<ZaloAdsPerformanceTrend[]>(
        `/marketing/zalo-ads/trends?period=${period}&days=${days}`
      );

      return response.result;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
    retry: 2,
  });
}
