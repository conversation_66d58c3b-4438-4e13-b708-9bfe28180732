import React from 'react';
import { useTranslation } from 'react-i18next';

import ModuleCard from '@/modules/components/card/ModuleCard';
import {ResponsiveGrid, Typography } from '@/shared/components/common';
import PageWrapper from '../components/common/PageWrapper';
import { IconName } from '@/shared/components/common';

/**
 * Trang chủ hiển thị các module ch<PERSON>h của hệ thống
 */
const Home: React.FC = () => {
  const { t } = useTranslation(['common', 'home', 'aiAgents', 'integrations', 'calendar', 'marketing', 'data', 'marketplace', 'business', 'rpoint', 'subscription', 'affiliate', 'tools']);

  // Danh sách các module chính
  const modules = [
    {
      id: 'ai-agents',
      title: t('aiAgents:aiAgents'),
      description: t('home:modules.aiAgents.description', 'Quản lý và tạo các AI agents thông minh cho doanh nghi<PERSON>'),
      icon: 'components' as IconName,
      linkTo: '/ai-agents',
      gradientColor: 'primary' as const,
    },
    {
      id: 'integrations',
      title: t('integrations:title'),
      description: t('home:modules.integrations.description', 'Tích hợp với các dịch vụ và API bên ngoài'),
      icon: 'integration' as IconName,
      linkTo: '/integrations',
      gradientColor: 'primary' as const,
    },
    {
      id: 'calendar',
      title: t('calendar:title'),
      description: t('home:modules.calendar.description', 'Quản lý lịch trình, sự kiện và cuộc họp'),
      icon: 'calendar' as IconName,
      linkTo: '/calendar',
      gradientColor: 'primary' as const,
    },
    {
      id: 'marketing',
      title: t('marketing:title'),
      description: t('home:modules.marketing.description', 'Chiến dịch marketing, email và SMS marketing'),
      icon: 'campaign' as IconName,
      linkTo: '/marketing',
      gradientColor: 'primary' as const,
    },
    {
      id: 'data',
      title: t('data:title'),
      description: t('home:modules.data.description', 'Quản lý dữ liệu, phân tích và báo cáo'),
      icon: 'chart' as IconName,
      linkTo: '/data',
      gradientColor: 'primary' as const,
    },
    {
      id: 'marketplace',
      title: t('marketplace:title'),
      description: t('home:modules.marketplace.description', 'Cửa hàng sản phẩm và dịch vụ'),
      icon: 'shopping-cart' as IconName,
      linkTo: '/marketplace',
      gradientColor: 'primary' as const,
    },
    {
      id: 'business',
      title: t('business:title'),
      description: t('home:modules.business.description', 'Quản lý thanh toán và thương mại'),
      icon: 'business' as IconName,
      linkTo: '/business',
      gradientColor: 'primary' as const,
    },
    {
      id: 'rpoint-packages',
      title: t('rpoint:packages.title'),
      description: t('home:modules.rpoint.description', 'Nạp và quản lý điểm R-Point'),
      icon: 'money' as IconName,
      linkTo: '/rpoint/packages',
      gradientColor: 'primary' as const,
    },
    {
      id: 'subscription-packages',
      title: t('subscription:title'),
      description: t('home:modules.subscription.description', 'Gói dịch vụ và đăng ký subscription'),
      icon: 'subscription' as IconName,
      linkTo: '/subscription/packages',
      gradientColor: 'primary' as const,
    },
    {
      id: 'affiliate',
      title: t('affiliate:title'),
      description: t('home:modules.affiliate.description', 'Chương trình tiếp thị liên kết và hoa hồng'),
      icon: 'link' as IconName,
      linkTo: '/user/affiliate',
      gradientColor: 'primary' as const,
    },
    {
      id: 'tools',
      title: t('tools:title'),
      description: t('home:modules.tools.description', 'Công cụ và tiện ích hỗ trợ'),
      icon: 'settings' as IconName,
      linkTo: '/tools',
      gradientColor: 'primary' as const,
    },
    {
      id: 'settings',
      title: t('common:settings'),
      description: t('home:modules.settings.description', 'Cài đặt hệ thống và tùy chỉnh'),
      icon: 'settings' as IconName,
      linkTo: '/settings',
      gradientColor: 'primary' as const,
    },
  ];

  return (
    <PageWrapper>
      <div className="mb-8">
        <Typography variant="h3" className="mb-2">
          {t('home:welcome', 'Chào mừng đến với RedAI')}
        </Typography>
        <Typography variant="body1" color="muted">
          {t('home:welcomeDescription', 'Hệ thống quản lý doanh nghiệp toàn diện')}
        </Typography>
      </div>

      <ResponsiveGrid
        gap={4}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2 }}
      >
        {modules.map(module => (
          <ModuleCard
            key={module.id}
            title={module.title}
            description={module.description}
            icon={module.icon}
            linkTo={module.linkTo}
            gradientColor={module.gradientColor}
            className="h-full"
          />
        ))}
      </ResponsiveGrid>
    </PageWrapper>
  );
};

export default Home;
